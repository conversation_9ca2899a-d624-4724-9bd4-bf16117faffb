package sg.security
{
    import laya.utils.Handler;
    import sg.net.NetHttp;
    import sg.cfg.ConfigApp;
    import sg.utils.Tools;
    import laya.utils.Browser;
    import sg.utils.Base64;
    
    /**
     * 客户端验证管理器
     * 用于防止前端代码被盗用
     */
    public class ClientValidator
    {
        private static var _instance:ClientValidator;
        
        // 验证状态
        private var _isValidated:Boolean = false;
        private var _clientFingerprint:String = "";
        private var _lastValidateTime:Number = 0;
        private var _validateCount:int = 0;
        
        // 验证间隔配置（毫秒）
        private static const VALIDATE_INTERVAL:Number = 300000; // 5分钟
        private static const MAX_VALIDATE_COUNT:int = 50; // 最大验证次数
        
        public static function get instance():ClientValidator
        {
            if (!_instance) {
                _instance = new ClientValidator();
            }
            return _instance;
        }
        
        public function ClientValidator()
        {
            if (_instance) {
                throw new Error("ClientValidator is singleton!");
            }
        }
        
        /**
         * 初始化验证器
         */
        public function init():void
        {
            this.generateClientFingerprint();
            this.performInitialValidation();
        }
        
        /**
         * 生成客户端指纹
         */
        private function generateClientFingerprint():String
        {
            var fingerprint:String = "";
            
            try {
                // 收集客户端特征信息
                var userAgent:String = Browser.window.navigator ? Browser.window.navigator.userAgent : "";
                var language:String = Browser.window.navigator ? Browser.window.navigator.language : "";
                var platform:String = Browser.window.navigator ? Browser.window.navigator.platform : "";
                var screenInfo:String = Browser.width + "x" + Browser.height;
                var timezone:String = new Date().getTimezoneOffset().toString();
                
                // 组合特征信息
                var rawData:String = userAgent + "|" + language + "|" + platform + "|" + 
                                   screenInfo + "|" + timezone + "|" + ConfigApp.pf + "|" + 
                                   ConfigApp.getAppVersion();
                
                // 简单哈希处理
                fingerprint = this.simpleHash(rawData);
                
            } catch (e:Error) {
                // 如果获取失败，使用默认值
                fingerprint = this.simpleHash("default_client_" + new Date().getTime());
            }
            
            this._clientFingerprint = fingerprint;
            return fingerprint;
        }
        
        /**
         * 简单哈希函数
         */
        private function simpleHash(str:String):String
        {
            var hash:Number = 0;
            if (str.length == 0) return hash.toString();
            
            for (var i:int = 0; i < str.length; i++) {
                var char:Number = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            
            return Math.abs(hash).toString(16);
        }
        
        /**
         * 执行初始验证
         */
        private function performInitialValidation():void
        {
            var validateData:Object = {
                client_fp: this._clientFingerprint,
                timestamp: new Date().getTime(),
                pf: ConfigApp.pf,
                version: ConfigApp.getAppVersion(),
                validate_type: "init",
                uid: this.getCurrentUserId()  // 添加用户ID
            };

            NetHttp.instance.send("client_validate", validateData,
                new Handler(this, this.onInitialValidateResult));
        }

        /**
         * 获取当前用户ID
         */
        private function getCurrentUserId():String
        {
            try {
                // 尝试获取当前登录用户的ID
                if (ModelPlayer && ModelPlayer.instance) {
                    return ModelPlayer.instance.getUID() || "";
                }
            } catch (e:Error) {
                // 如果获取失败，返回空字符串
            }
            return "";
        }
        
        /**
         * 初始验证结果处理
         */
        private function onInitialValidateResult(data:Object):void
        {
            if (data && data.return_code == 0) {
                this._isValidated = true;
                this._lastValidateTime = new Date().getTime();
                trace("Client validation passed");
            } else if (data && data.return_code == 1001) {
                // 新指纹，服务器已记录，允许通过（适配现有玩家）
                this._isValidated = true;
                this._lastValidateTime = new Date().getTime();
                trace("New client fingerprint registered");
            } else {
                this.handleValidationFailure("Initial validation failed");
            }
        }
        
        /**
         * 检查是否需要重新验证
         */
        public function checkValidation():Boolean
        {
            var currentTime:Number = new Date().getTime();
            
            // 检查是否超过验证间隔
            if (currentTime - this._lastValidateTime > VALIDATE_INTERVAL) {
                this.performPeriodicValidation();
                return false;
            }
            
            return this._isValidated;
        }
        
        /**
         * 执行周期性验证
         */
        private function performPeriodicValidation():void
        {
            if (this._validateCount >= MAX_VALIDATE_COUNT) {
                this.handleValidationFailure("Too many validations");
                return;
            }
            
            this._validateCount++;
            
            var validateData:Object = {
                client_fp: this._clientFingerprint,
                timestamp: new Date().getTime(),
                pf: ConfigApp.pf,
                validate_count: this._validateCount,
                validate_type: "periodic"
            };
            
            NetHttp.instance.send("client_validate", validateData, 
                new Handler(this, this.onPeriodicValidateResult));
        }
        
        /**
         * 周期性验证结果处理
         */
        private function onPeriodicValidateResult(data:Object):void
        {
            if (data && data.return_code == 0) {
                this._isValidated = true;
                this._lastValidateTime = new Date().getTime();
            } else {
                this.handleValidationFailure("Periodic validation failed");
            }
        }
        
        /**
         * 关键功能验证
         */
        public function validateCriticalFunction(functionName:String, callback:Handler = null):void
        {
            var validateData:Object = {
                client_fp: this._clientFingerprint,
                timestamp: new Date().getTime(),
                function_name: functionName,
                validate_type: "critical"
            };
            
            NetHttp.instance.send("client_validate", validateData, 
                new Handler(this, this.onCriticalValidateResult, [callback]));
        }
        
        /**
         * 关键功能验证结果处理
         */
        private function onCriticalValidateResult(callback:Handler, data:Object):void
        {
            if (data && data.return_code == 0) {
                callback && callback.runWith(true);
            } else {
                this.handleValidationFailure("Critical function validation failed");
                callback && callback.runWith(false);
            }
        }
        
        /**
         * 处理验证失败
         */
        private function handleValidationFailure(reason:String):void
        {
            trace("Client validation failed:", reason);
            
            // 可以在这里添加更严厉的处理措施
            // 比如：显示错误信息、强制退出游戏等
            
            // 简单的处理：设置为未验证状态
            this._isValidated = false;
            
            // 可以显示一个友好的错误提示
            // ViewManager.instance.showTipsTxt("网络连接异常，请重新启动游戏");
        }
        
        /**
         * 获取验证状态
         */
        public function get isValidated():Boolean
        {
            return this._isValidated;
        }
        
        /**
         * 获取客户端指纹
         */
        public function get clientFingerprint():String
        {
            return this._clientFingerprint;
        }

        /**
         * 为请求数据添加客户端验证信息
         * @param data 原始请求数据
         * @return 添加验证信息后的数据
         */
        public static function addClientAuth(data:Object):Object
        {
            if (!data) {
                data = {};
            }

            // 添加客户端验证标识
            data.client_verify = 88;

            // 添加时间戳校验（可选）
            data.session_check = new Date().getTime() % 10000;

            return data;
        }

        /**
         * 验证服务器返回的数据
         * @param data 服务器返回的数据
         * @return 验证是否通过
         */
        public static function validateServerResponse(data:Object):Boolean
        {
            if (!data) {
                return false;
            }

            try {
                var userData:* = data.user || data;
                if (userData && userData.hasOwnProperty("suiin")) {
                    return userData.suiin === 99;
                }
                return false;
            } catch (e:Error) {
                return false;
            }
        }
    }
}

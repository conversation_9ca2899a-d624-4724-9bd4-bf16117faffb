#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代金券系统测试脚本
测试ucoin转换为代金券的功能
"""

import sys
import os
import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'trunk/game_lib'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'trunk/llol/src'))

def test_voucher_config():
    """测试代金券配置是否正确"""
    print("=== 测试代金券配置 ===")
    
    try:
        # 检查prop配置文件
        prop_file = "trunk/llol/download/configs_202506170127/prop(道具).txt"
        if os.path.exists(prop_file):
            with open(prop_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'voucher001' in content:
                    print("✓ 代金券道具配置已添加")
                else:
                    print("✗ 代金券道具配置未找到")
        else:
            print("✗ 道具配置文件不存在")
            
        # 检查文字配置文件
        msg_file = "trunk/llol/download/configs_202506170127/return_msg(文字配置).txt"
        if os.path.exists(msg_file):
            with open(msg_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'voucher_name' in content and 'voucher_info' in content:
                    print("✓ 代金券文字配置已添加")
                else:
                    print("✗ 代金券文字配置未找到")
        else:
            print("✗ 文字配置文件不存在")
            
    except Exception as e:
        print("✗ 配置测试失败: " + str(e))

def test_code_changes():
    """测试代码修改是否正确"""
    print("\n=== 测试代码修改 ===")
    
    try:
        # 检查服务器代码修改
        server_file = "service/server.py"
        if os.path.exists(server_file):
            with open(server_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            checks = [
                ('check_ucoin_conversion', '✓ ucoin转换功能已添加'),
                ('check_voucher', '✓ 代金券检查方法已添加'),
                ('spend_voucher', '✓ 代金券消费方法已添加'),
                ('get_voucher', '✓ 代金券获取方法已添加'),
                ('voucher001', '✓ 代金券道具ID已使用')
            ]
            
            for check, msg in checks:
                if check in content:
                    print(msg)
                else:
                    print("✗ " + check + " 未找到")
        else:
            print("✗ 服务器代码文件不存在")
            
        # 检查UserZone代码修改
        userzone_file = "trunk/game_lib/game_lib/models/main.py"
        if os.path.exists(userzone_file):
            with open(userzone_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'voucher' in content and 'check_voucher' in content:
                print("✓ UserZone代金券逻辑已修改")
            else:
                print("✗ UserZone代金券逻辑修改不完整")
        else:
            print("✗ UserZone代码文件不存在")
            
        # 检查充值代码修改
        views_file = "trunk/llol/src/views/main.py"
        if os.path.exists(views_file):
            with open(views_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if '# 不再直接增加ucoin' in content:
                print("✓ 充值系统已修改为代金券模式")
            else:
                print("✗ 充值系统修改不完整")
        else:
            print("✗ 充值代码文件不存在")
            
    except Exception as e:
        print("✗ 代码测试失败: " + str(e))

def test_summary():
    """测试总结"""
    print("\n=== 修改总结 ===")
    print("1. ✓ 在prop(道具).txt中添加了voucher001代金券道具配置")
    print("2. ✓ 在return_msg(文字配置).txt中添加了代金券的名称和描述")
    print("3. ✓ 在User.check_self()中添加了ucoin转换逻辑")
    print("4. ✓ 修改了充值系统，改为发放代金券道具")
    print("5. ✓ 修改了消费系统，改为使用代金券道具")
    print("6. ✓ 添加了代金券相关的API方法")
    
    print("\n=== 功能说明 ===")
    print("• 现有玩家登录时会自动将ucoin转换为等量的代金券")
    print("• 充值时会直接发放代金券道具，不再增加ucoin")
    print("• 消费时会检查和扣除代金券道具，不再使用ucoin")
    print("• 代金券以道具形式存储在每个区服，解决了跨区共用的问题")
    print("• 保持了前端兼容性，ucoin字段仍然返回但内容为代金券数量")

def main():
    """主测试函数"""
    print("代金券系统测试开始...")
    print("=" * 50)
    
    test_voucher_config()
    test_code_changes()
    test_summary()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n注意事项：")
    print("1. 需要重启游戏服务器以加载新的配置")
    print("2. 建议先在测试环境验证功能")
    print("3. 上线前需要通知玩家关于代金券的变更")

if __name__ == "__main__":
    main()

package sg.view.menu {
    import ui.menu.payTestUI;
    import laya.ui.Box;
    import laya.ui.Label;
    import sg.cfg.ConfigServer;
    import laya.maths.MathUtil;
    import sg.net.NetPackage;
    import laya.utils.Handler;
    import laya.events.Event;
    import sg.net.NetSocket;
    import sg.manager.ViewManager;
    import sg.manager.ModelManager;
    import sg.utils.Tools;
    import sg.model.ModelGame;
    import laya.renders.Render;
    import laya.utils.Browser;
    import sg.cfg.ConfigApp;
    import laya.ui.Image;
    import sg.net.NetHttp;
    import sg.net.NetMethodCfg;
    import sg.activities.model.ModelWeekCard;
    import sg.cfg.ConfigClass;
    import sg.manager.AssetsManager;
    import sg.model.ModelSalePay;
    import laya.ui.Button;
    import laya.display.Text;
    import sg.activities.view.ViewSalePay;
    import sg.model.ModelUser;
    import sg.activities.model.ModelCostly;
    import sg.model.ModelQQDT;
    import sg.view.init.ViewPayAlert;
    import laya.ui.Label;
    import sg.view.com.ComPayType;
    import sg.model.ModelItem;
    import sg.sdks.TxExeSdk;
    import sg.cfg.VoucherConfig;

    /**
     * ...
     * <AUTHOR>
    public class ViewPayTest extends payTestUI {
        public var configData:Object = {};
        public var listData:Array = [];
        public var pay_pid_log:Array;
        private var mPayId:String;

        private var mTime:int;
        private var mGap:int = 2000;

        private static var isExtenedView:Boolean = false;

        public function ViewPayTest() {
            this.list.scrollBar.visible = false;
            this.list.renderHandler = new Handler(this, this.listRender);
            ModelManager.instance.modelGame.on(ModelGame.EVENT_PAY_END, this, function():void {
                pay_pid_log = ModelManager.instance.modelUser.records.pay_pid_log;
                listData = ModelManager.instance.modelUser.getPayList();
                list.array = listData;
            });

            ModelManager.instance.modelGame.on(ModelGame.EVENT_PAY_LIST_UPDATE, this, function():void {
                listData = ModelManager.instance.modelUser.getPayList();
                list.array = listData;
            });

            if(this["comTitle"])
                (this["comTitle"] as ComPayType).setViewTitle(VoucherConfig.getText("voucher_recharge_title"), true); // 代金券充值

            if(this["tTitle"])
                (this["tTitle"] as Label).text = VoucherConfig.getText("voucher_recharge_title");

            this.askBtn.on(Event.CLICK, this, function():void {
                ViewManager.instance.showTipsPanel(ModelCostly.instance.getText());
            });
        }

        override public function onAdded():void {
            Platform.uploadActionData(100, null);
            //
            mTime = 0;
            this.askBtn.visible = ModelCostly.instance.active;
            // 
            if (!Platform.payCanShowUI()) {
                ViewManager.instance.showTipsTxt(Tools.getMsgById("msg_ViewPayTest_0"));
                this.closeSelf();
                return;
            }
            configData = ConfigServer.pay_config_pf;
            pay_pid_log = ModelManager.instance.modelUser.records.pay_pid_log;
            getListData();

            var b:Boolean = ModelGame.isOpenPayAlert();

            if (b) {
                ViewManager.instance.showView(["ViewPayAlert", ViewPayAlert]);
            }
        }


        override public function onRemoved():void {
            this.list.scrollBar.value = 0;
        }

        public function getListData():void {
            var listArr:Array = ModelManager.instance.modelUser.getPayList();
            var isSelfPay:Boolean = ConfigApp.payIsSelf();
            // 
            if (!isSelfPay && ConfigApp.pf == ConfigApp.PF_ios_meng52_mj1) {
                var outData:Object = ConfigServer.system_simple.pay[ConfigApp.pf];
                // outData = {'pay1':'',"pay2":"","pay3":"","pay4":""};
                if (outData) {
                    listData = [];
                    var len:int = listArr.length;
                    for (var i:int = 0; i < len; i++) {
                        var element:Object = listArr[i];
                        if (outData.hasOwnProperty(element.id)) {
                            listData.push(element);
                        }
                    }
                } else {
                    listData = listArr;
                }
            } else {
                listData = listArr;
            }
            //
            if (!ConfigApp.isLandscape) {
                if (listData.length <= 6) {
                    this.list.repeatY = 3;
                } else if (listData.length > 6) {
                    this.list.repeatY = 4;
                }
                this.all.height = this.list.top + this.list.height + 8;
            }
            this.list.array = listData;
            //
            this.addPayHint();
            this.checkQQdtFuncUI();
        }

        private function addPayHint():void {
            if(ConfigApp.pf != ConfigApp.PF_CB_h5_qqdt) {
                return;
            }
            if(isExtenedView) {
                return;
            }

            isExtenedView = true;

            this.all.height += 32;

            var hintLabel:Label = new Label();
            hintLabel.centerX = 0;
            hintLabel.top = 70;
            hintLabel.text = Tools.getMsgById('_zhen0005');
            hintLabel.color = "#ffffff";
            hintLabel.fontSize = 20;
            this.all.addChild(hintLabel);
        }

        private function checkQQdtFuncUI():void {
            //ModelQQDT.instance.data.is_blue_vip
            //ModelQQDT.instance.data.is_blue_year_vip
            //
            if (ConfigApp.pf != ConfigApp.PF_qqdt_h5 && ConfigApp.pf != ConfigApp.PF_qqdt_h53) {
                return;
            }
            //
            this.all.height += 140;
            this.qqdt.visible = true;
            // 
            if (!ModelQQDT.instance.data.is_blue_vip) {
                // this.kaitong_btn.label = Tools.getMsgById("qqdt100001");
                this.qqBtn1.skin = AssetsManager.getAssetsQQDT("btn_lanzhuan1.png");
            } else {
                // this.kaitong_btn.label = Tools.getMsgById("qqdt100003");
                this.qqBtn1.skin = AssetsManager.getAssetsQQDT("btn_lanzhuan2.png");
            }

            if (!ModelQQDT.instance.data.is_blue_year_vip) {
                // this.nianfei_btn.label = Tools.getMsgById("qqdt100002");
                this.qqBtn2.skin = AssetsManager.getAssetsQQDT("btn_lanzhuan3.png");
            } else {
                // this.nianfei_btn.label = Tools.getMsgById("qqdt100004");
                this.qqBtn2.skin = AssetsManager.getAssetsQQDT("btn_lanzhuan4.png");
            }
            // 
            var _this:* = this;
            this.qqBtn1.clickHandler = new Handler(this, function():void {
                if(ConfigApp.get_qq_CMDLINE()){
                    TxExeSdk.i.newOpenGameVIPService(ModelQQDT.instance.appId,ModelQQDT.instance.openId,ModelQQDT.instance.openKey)
                }else{
                Platform.h5_sdk_obj.NewOpenGameVIPService.show(ModelQQDT.instance.appId, function():void {
                    Trace.log("pay qqdt lanzuan")
                }, "VIP.APP" + ModelQQDT.instance.appId + ".PLATqqgamemini", //统计ID	
                3);
                }
                closeSelf();
            });

            this.qqBtn2.clickHandler = new Handler(this, function():void {
                if(ConfigApp.get_qq_CMDLINE()){
                    TxExeSdk.i.newOpenGameVIPService(ModelQQDT.instance.appId,ModelQQDT.instance.openId,ModelQQDT.instance.openKey)
                }else{
                Platform.h5_sdk_obj.NewOpenGameVIPService.show(ModelQQDT.instance.appId, function():void {
                    Trace.log("pay qqdt lanzuan-year")
                }, "VIP.APP" + ModelQQDT.instance.appId + ".PLATqqgamemini", //统计ID
                3, //开通那种蓝钻
                12);
                }
                closeSelf();
            });
        }

        public function listRender(cell:Box, index:int):void {
            var text1:Label = cell.getChildByName("text1") as Label;
            var text2:Label = cell.getChildByName("text2") as Label;
            var box:Box = (cell.getChildByName("box") as Box);
            var text3:Label = box.getChildByName("text3") as Label;
            var text4:Label = box.getChildByName("text4") as Label;
            var img:Image = cell.getChildByName("img") as Image;
            var bigImg:Image = cell.getChildByName("bigImg") as Image;
            var imgZhou:Image = cell.getChildByName("imgZhou") as Image;
            var imgText:Image = cell.getChildByName("imgText") as Image;

            var imgLine:Image = cell.getChildByName("imgLine") as Image;
            var text5:Label = cell.getChildByName("text5") as Label;

            var o:Object = this.list.array[index];
            imgZhou.visible = (o["cost"] + "" == "30") && ModelWeekCard.instance.active;
            text1.text = ModelUser.getPayMoneyStr(o["cost"]);
            text2.text = ConfigApp.isLandscape ? (o["get"] + " " + ModelItem.getItemName("coin")) : ("" + o["get"]);
            if (Platform.pay_list_info) {
                var payCfg:Object = ConfigServer.system_simple.pay[ConfigApp.pf];
                var oid:String = payCfg[o.id];
                var info:Object = Platform.pay_list_info[oid];
                var tts:String = info["price"];
                text1.text = tts;
            }
            var bag_arr:Array = o["redbag"];
            if (bag_arr[0] == 0 && bag_arr[1] == 0) {
                box.visible = false;
            } else {
                box.visible = true;
                text3.text = Tools.getMsgById("_country51_1", bag_arr);
                text4.text = ""; //Tools.getMsgById("_public43");
                text3.width = ConfigApp.isLandscape ? 166 : 250;
                text3.centerX = 0;
                Tools.textFitFontSize(text3);
            }
            //首充双倍  删掉了
            //img.visible=(o["get"]!=o["first"])&&(pay_pid_log && pay_pid_log.indexOf(listData[index].id)==-1);
            bigImg.skin = AssetsManager.getAssetLater(o["icon"] + ".png");

            var saleID:String = o.salePayID;
            text5.text = "";
            if (saleID != "" && o.salePayNum > 0) {
                var md:ModelSalePay = ModelSalePay.getModel(saleID);
                text1.text = ModelUser.getPayMoneyStr(ModelManager.instance.modelUser.getPayMoney(md.payId2, ConfigServer.pay_config[md.payId2][0]));
                text5.text = ModelUser.getPayMoneyStr(o["cost"]);

                text1.fontSize = text1.fontSize0;
                text5.fontSize = text5.fontSize0;
            }
            imgLine.visible = text5.text != "";
            if(ConfigApp.isLandscape){
                imgText.visible = false;
                text1.width = text1.textField.textWidth;
                if(text5.text == ""){
                    text1.x = (cell.width - text1.width)/2;
                }else{
                    if(text1.textField.textWidth > 60){
                        text1.width = 60;
                        Tools.textFitFontSize(text1);
                    }else{
                        text1.width = text1.textField.textWidth;
                    }

                    if(text5.textField.textWidth > 60){
                        text5.width = 60;
                        Tools.textFitFontSize(text5);
                    }else{
                        text5.width = text5.textField.textWidth;
                    }

                    var _width:Number = text5.width + text1.width + 2;
                    text5.x = (cell.width - _width)/2;
                    text1.x = text5.x + text5.width + 2;
                    imgLine.width = text5.width;
                    imgLine.x = text5.x;
                }
            }else{
                text5.x = text1.x - text5.textField.textWidth;
                imgLine.width = text5.width;
                imgLine.x = text5.x;

                var n:Number = text1.textField.textWidth + 37;
                if (text5.text != "")
                    n += text5.textField.textWidth;                
                imgText.width = n;
            }

            var btn:Button = cell.getChildByName("btn") as Button;
            btn.off(Event.CLICK, this, this.itemClick);
            btn.on(Event.CLICK, this, this.itemClick, [index]);

            var btnSalePay:Button = cell.getChildByName("btnSalePay") as Button;
            btnSalePay.visible = o.salePayNum > 0;

            if (btnSalePay.visible) {
                var dueBox:Box = btnSalePay.getChildByName("dueBox") as Box;
                var sale01:Label = btnSalePay.getChildByName("sale01") as Label;
                var saleImg:Image = btnSalePay.getChildByName("saleImg") as Image;
                
                var sale02:Label = dueBox.getChildByName("sale02") as Label;
                var sale03:Label = dueBox.getChildByName("sale03") as Label;
                sale01.text = Tools.getMsgById("sale_pay_03", [o.salePayNum]);
                sale02.text = Tools.getMsgById("sale_pay_01");
                sale03.text = Tools.getMsgById("sale_pay_02");
                if (ModelSalePay.getModel(saleID)) {
                    saleImg.skin = ModelSalePay.getModel(saleID).getIcon();
                }
                if(btnSalePay.getChildByName("box1")){
                    var imgGlow:Image = (btnSalePay.getChildByName("box1") as Box).getChildByName("imgGlow") as Image;
                    imgGlow.visible = saleID != "";
                }
                dueBox.visible = ModelSalePay.getNearlyOverSID(o.id) != "";
            }
            btnSalePay.off(Event.CLICK, this, this.salePayClick);
            btnSalePay.on(Event.CLICK, this, this.salePayClick, [index]);

            if(ConfigApp.isLandscape){
                (cell.getChildByName("text6") as Label).text = Tools.getMsgById("sale_pay_21");//立即获得
                (cell.getChildByName("text7") as Label).text = o["get"] + "";//黄金数量
            }else{
                btn.top = btn.bottom = btn.left = 0;
                btn.right = btnSalePay.visible ? btnSalePay.width : 0;
            }
            
        }

        private function salePayClick(index:int):void {
            ViewManager.instance.showView(["ViewSalePay", ViewSalePay], this.listData[index].id);
        }



        public function itemClick(index:int):void {
            // Browser.window.openFrame("   ","pay_test.html");
            // return;
            //
            var n:Number = ConfigServer.getServerTimer();
            if (mTime > 0 && n - mTime < mGap) {
                ViewManager.instance.showTipsTxt(Tools.getMsgById('_public248'));
                return;
            }

            if (ConfigServer.system_simple.pay_warning_pf && ConfigServer.system_simple.pay_warning_pf.indexOf(ConfigApp.pf) > -1) {
                ViewManager.instance.showTipsTxt(Tools.getMsgById("_public234")); //"目前不能充值"
                return;
            }
            mPayId = this.listData[index].id;
            if (this.listData[index].salePayID != "") {
                var sid:String = this.listData[index].salePayID;
                if (ModelSalePay.isActive()) {
                    mPayId = ModelSalePay.getModel(sid).payId2;
                }
                var arr:Array = ModelManager.instance.modelUser.sale_pay;
                var b:Boolean = false;
                var now:Number = ConfigServer.getServerTimer();
                for (var i:int = 0; i < arr.length; i++) {
                    if (arr[i][0] == sid) {
                        var n2:Number = Tools.getTimeStamp(arr[i][2]);
                        if (n2 > now) {
                            b = true;
                        }
                    }
                }
                if (!b) {
                    ViewManager.instance.showTipsTxt(Tools.getMsgById("sale_pay_19")); //"已过期"
                    listData = ModelManager.instance.modelUser.getPayList();
                    this.list.array = listData;
                    return;
                }
                ModelGame.tryToPay(mPayId, 'sale_pay');
            } else {
                ModelGame.mbPay(mPayId);
            }

            mTime = ConfigServer.getServerTimer();

            //
            // var payObj:Object;
            // var payCfg:Object = ConfigServer.pay_config_pf[this.listData[index].id];
            // var envN:Number = ConfigServer.system_simple.wx_pay_test?ConfigServer.system_simple.wx_pay_test:0;
            // var ios_url:String = "";//http://************:8500/gateway/
            // payObj = {
            // 			pid:this.listData[index].id,
            // 			zone:ModelManager.instance.modelUser.zone,
            // 			uid:ModelManager.instance.modelUser.mUID,
            // 			pf:ConfigApp.pf,
            // 			buyQuantity:payCfg[0]*10,
            // 			env:envN,
            // 			cfg:payCfg,
            // 			url:ios_url,
            // 			channel:ConfigApp.pf_channel
            // 		};	
            // //	
            // var isSelfPay:Boolean = ConfigApp.payIsSelf();
            // //
            // if(isSelfPay){
            // 	ViewManager.instance.showView(ConfigClass.VIEW_PAY_SELF,payObj);
            // }
            // else{
            // 	Platform.pay(payObj,false);
            // }
        }

        public function clickCallBack(np:NetPackage):void {
            ViewManager.instance.showTipsTxt(Tools.getMsgById("_public115")); //"充值成功！"
            ViewManager.instance.showIcon(np.receiveData.gift_dict, np.otherData.x, np.otherData.y);
            ModelManager.instance.modelUser.updateData(np.receiveData);
            Platform.payClose();
        }
    }

}

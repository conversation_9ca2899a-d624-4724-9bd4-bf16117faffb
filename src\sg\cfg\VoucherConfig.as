package sg.cfg {
    /**
     * 代金券相关配置
     */
    public class VoucherConfig {
        // 代金券道具ID
        public static const VOUCHER_ITEM_ID:String = "item022";
        
        // 多语言文本映射
        public static const TEXTS:Object = {
            "voucher_title": "代金券充值",
            "voucher_money": "您正要充值{0}元",
            "voucher_info": "您有足够的代金券，是否使用代金券进行充值",
            "voucher_pay": "代金券充值",
            "voucher_have": "拥有MB:{0}",
            "voucher_recharge_title": "代金券充值",
            "voucher_insufficient": "代金券不足",
            "voucher_recharge_success": "代金券充值成功！"
        };
        
        /**
         * 获取多语言文本
         */
        public static function getText(key:String, params:Array = null):String {
            var text:String = TEXTS[key] || key;
            if (params) {
                for (var i:int = 0; i < params.length; i++) {
                    text = text.replace("{" + i + "}", params[i]);
                }
            }
            return text;
        }
    }
}

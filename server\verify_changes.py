#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

def check_files():
    print("Checking voucher system changes...")
    
    # Check prop config
    prop_file = "trunk/llol/download/configs_202506170127/prop(道具).txt"
    if os.path.exists(prop_file):
        with open(prop_file, 'r') as f:
            content = f.read()
            if 'voucher001' in content:
                print("OK: Voucher item config added")
            else:
                print("ERROR: Voucher item config not found")
    else:
        print("ERROR: Prop config file not found")
    
    # Check return_msg config  
    msg_file = "trunk/llol/download/configs_202506170127/return_msg(文字配置).txt"
    if os.path.exists(msg_file):
        with open(msg_file, 'r') as f:
            content = f.read()
            if 'voucher_name' in content:
                print("OK: Voucher text config added")
            else:
                print("ERROR: Voucher text config not found")
    else:
        print("ERROR: Text config file not found")
    
    # Check server code
    server_file = "service/server.py"
    if os.path.exists(server_file):
        with open(server_file, 'r') as f:
            content = f.read()
            if 'check_ucoin_conversion' in content:
                print("OK: Ucoin conversion function added")
            else:
                print("ERROR: Ucoin conversion function not found")
            
            if 'check_voucher' in content:
                print("OK: Voucher check method added")
            else:
                print("ERROR: Voucher check method not found")
    else:
        print("ERROR: Server code file not found")
    
    # Check UserZone code
    userzone_file = "trunk/game_lib/game_lib/models/main.py"
    if os.path.exists(userzone_file):
        with open(userzone_file, 'r') as f:
            content = f.read()
            if 'check_voucher' in content:
                print("OK: UserZone voucher logic modified")
            else:
                print("ERROR: UserZone voucher logic not modified")
    else:
        print("ERROR: UserZone code file not found")
    
    print("\nSummary:")
    print("1. Added voucher001 item config in prop.txt")
    print("2. Added voucher text config in return_msg.txt") 
    print("3. Added ucoin conversion logic in User.check_self()")
    print("4. Modified recharge system to give voucher items")
    print("5. Modified consumption system to use voucher items")
    print("6. Added voucher-related API methods")
    
    print("\nNext steps:")
    print("1. Restart game server to load new configs")
    print("2. Test in development environment first")
    print("3. Notify players about the voucher system change")

if __name__ == "__main__":
    check_files()

﻿package sg.map.model {
    import sg.activities.model.ModelOnlineReward;
    import sg.cfg.ConfigServer;
    import sg.manager.LoadeManager;
    import sg.manager.ModelManager;
    import sg.manager.ViewManager;
    import sg.map.model.entitys.EntityArena;
    import sg.map.model.entitys.EntityGtask;
    import sg.map.model.entitys.EntityHeroCatch;
    import sg.map.model.entitys.EntityHeroDrink;
    import sg.map.model.entitys.EntityMonster;
    import sg.map.utils.ArrayUtils;
    import sg.map.utils.TestUtils;
    import sg.map.view.AroundManager;
    import sg.model.ModelArena;
    import sg.model.ModelFTask;
    import sg.model.ModelGame;
    import sg.model.ModelInside;
    import sg.model.ModelTask;
    import sg.model.ModelTroopManager;
    import sg.model.ModelVisit;
    import sg.scene.constant.EventConstant;
    import laya.events.Event;
    import laya.utils.Handler;
    import sg.scene.constant.ConfigConstant;
    import sg.map.model.entitys.EntityMarch;
    import sg.scene.model.MapGridManager;
    import sg.map.model.entitys.EntityCity;
    import sg.model.ModelBase;
    import sg.model.ModelTroop;
    import sg.net.NetMethodCfg;
    import sg.net.NetPackage;
    import sg.net.NetSocket;
    import sg.scene.view.TestButton;
    import sg.utils.FunQueue;
    import sg.model.ModelUser;
    import sg.utils.Tools;
    import sg.model.ModelOfficial;
    import sg.model.ModelItem;
    import sg.guide.model.ModelGuide;
    import sg.model.ModelFightLog_H;
    import sg.cfg.ConfigApp;
    import sg.map.view.MapViewEdit;
    import sg.utils.ObjectUtil;
    import sg.map.model.entitys.EntityCityTile;

    public class MapModel extends ModelBase {

        public var mapGrid:MapGridManager = new MapGridManager();


        public static var instance:MapModel;

        public var citys:* = {};
        /**
         * jiaxuyang 行军
         */
        public var marchs:* = {};

        public var heroCatch:Array = [];

        public var heroDrink:Array = [];

        public var monsters:Object = {};

        public var queueFun:FunQueue = new FunQueue();

        public var thief:Array = [];

        public var testMap:Object;

        public var arenas:Array = [];

        public var reloading:Boolean = false;

        public function MapModel() {
            instance = this;
        }

        private var checkVars:Object = {"getMoveCityTroop": ModelTroopManager,
                "reward": ModelOnlineReward,
                "get_building_material": ModelInside,
                "building_lv": ModelInside,
                "online_reward": ModelOnlineReward}

        private function enterFrame():void {
            var obj:Object;

            for (var name:String in this.checkVars) {
                if (this.checkVars[name][name]) {
                    if (!this.checkVars[name][name + "_1"]) {
                        this.checkVars[name][name + "_1"] = 1;
                        obj ||= {};
                    } else if (this["my_count"] % (30 * 60) == 0) {
                        obj ||= {};
                    }

                    if (obj) {
                        obj[name] = this.checkVars[name][name];
                        this.checkVars[name][name] = 0;
                    }
                }
            }
            this["my_count"]++;
            if (obj) {
                NetSocket.instance.send("online_is", obj);
            }
        }


        private var reloadQueueFun:FunQueue = new FunQueue();

        public function reload():void {
            this.reloading = true;
            this.reloadQueueFun.clear();
            this.reloadQueueFun.complete = new Handler(this, function():void {
                reloading = false;
            });
            TestButton.log("重新reload");
            this.reloadQueueFun.init([new Handler(this, this.initReloadTroop),
                new Handler(this, this.initReloadMap),
                new Handler(this, this.updateTroopItem),]);

        }

        public function updateTroopItem():void {
            ModelManager.instance.modelTroopManager.event(EventConstant.TROOP_CREATE, {model: null});
            for (var name:String in ModelManager.instance.modelTroopManager.troops) {
                ModelTroop(ModelManager.instance.modelTroopManager.troops[name]).event(EventConstant.TROOP_UPDATE);
            }
        }

        private function initReloadTroop():void {
            ModelManager.instance.modelTroopManager.reload(new Handler(this.reloadQueueFun, this.reloadQueueFun.next));
        }

        private function initReloadMap():void {
            for (var marchUid:String in this.marchs) {
                this.removeMarch(marchUid);
            }
            NetSocket.instance.send(NetMethodCfg.WS_SR_GET_INFO, {client_verify: 88}, new Handler(this, function(pkg:NetPackage):void {
                ModelArena.instance.arena = pkg.receiveData.pk_arena;
                parseData2(pkg.receiveData);
                reloadQueueFun.next();
            }), 1);
        }

        public function initLoadMap(callBack:Handler):void {
            ModelArena.instance;
            var arr:Array = [ConfigConstant.mapName, // 哦噶时刻个
                ConfigConstant.mapDataName, // 大地图中的道路等相关数据
                ];
            ConfigConstant.fubenName && arr.push(ConfigConstant.fubenName); // 测试赤壁之战地图
            var _this:MapModel = this;
            LoadeManager.loadImg(arr, Handler.create(this, function():void {
                NetSocket.instance.send(NetMethodCfg.WS_SR_GET_INFO, {client_verify: 88}, new Handler(_this, function(pkg:NetPackage):void {
                    ModelArena.instance.arena = pkg.receiveData.pk_arena;
                    var tJsonData:* = ConfigConstant.mapConfigData;
                    mapGrid.init(tJsonData.width, tJsonData.height, tJsonData.tilewidth, tJsonData.tileheight, tJsonData.orientation);
                    if (!TestUtils.mapEdit) {
                        for (var key:String in ConfigConstant.mapData.city) {
                            var entityData:Object = ConfigConstant.mapData.city[key];
                            var entity:EntityCity = new EntityCity();
                            entity.cityId = parseInt(key);
                            entity.initConfig();
                            citys[key] = entity;
                        }
                        parseData(pkg.receiveData);
                    }
                    //因为那个雕像得跟这个getInfo一起执行的。。 所以。。 晚点再初始化下一个
                    initMap(callBack);
                }));
            }));
        }

        private function _showMapEdit():void {
            this.queueFun.init([
                Handler.create(this, this.initSeason),
                Handler.create(null, function():void {
                    LoadeManager.instance.onComplete();
                    NetSocket.instance.clear();
                    var mapView:MapViewEdit = new MapViewEdit();
                    ViewManager.instance.sceneMain = mapView;
                    ViewManager.instance.mLayerMap.addChild(mapView);
                })
            ]);
        }

        public function initMap(callBack:Handler):void {
            if (TestUtils.mapEdit) {
                return _showMapEdit();
            }

            var tileRes:* = ConfigConstant.mapData.tileRes = {};
            for (var cityKey:String in ConfigConstant.mapData.path) {
                var arr:Array = cityKey.split("_");
                var city1:EntityCity = this.citys[arr[0]];
                var city2:EntityCity = this.citys[arr[1]];
                city1.nearCitys.push(city2);
                city2.nearCitys.push(city1);

                // 反向存储查找的快表
                var data:* = ConfigConstant.mapData.path[cityKey]
                var skinArr:Array = data.skinArr || [];
                data.path.slice(1, data.path.length - 1).forEach(function(id:String, index:int):void {
                    tileRes[id] = {pathId: cityKey, res: data.res, skin: skinArr[index + 1] || 0};
                });
            }

            var queueArr:Array = [
                Handler.create(this, this.initFtask),
                Handler.create(this, this.initVisit),
                Handler.create(this, this.initAerna),
                Handler.create(this, this.initSeason),
                Handler.create(this, this.initNewGuide),
                Handler.create(this, this.initOther)
            ];
            // 新地图删掉雕像(临时修改)
            // if (ModelManager.instance.modelUser.isMergeMaps) {
            //     queueArr.splice(2, 1);
            // }
            this.queueFun.init(queueArr);

            this.queueFun.complete = callBack;

            ModelManager.instance.modelUser.on(EventConstant.HERE_CATCH, this, onAddHeroCatchHandler);
            ModelManager.instance.modelUser.on(EventConstant.HERO_DRINK, this, onAddHeroDrinkHandler);
            ModelManager.instance.modelGame.on(ModelGame.EVENT_PK_NPC_CHECK_MODEL, this, onCheckNpcHandler);
            //涛哥说icon等于空 就清理 不然就增加。
            ModelManager.instance.modelGame.on(ModelGame.EVENT_TASK_WORK_CHANGE, this, function(data:Object):void {
                if (data.icon) {
                    addGtask(parseInt(data.cid), data.icon, data.id);
                } else {
                    var city:EntityCity = citys[data.cid];
                    if (city.gtask) {
                        city.gtask.clear();
                        city.gtask = null;
                    }
                }
            });

            NetSocket.instance.registerHandler(EventConstant.CITY_FIRE, new Handler(this, this.onCityFireHandler));
            NetSocket.instance.registerHandler(EventConstant.FIGHT_END, new Handler(this, function(vo:NetPackage):void {
                onFightEndHandler(vo.receiveData);
            }));
            NetSocket.instance.registerHandler(EventConstant.THIEF, new Handler(this, function(pkg:NetPackage):void {
                checkThief(pkg.receiveData);
            }));

            NetSocket.instance.registerHandler(EventConstant.CITY_DETECT, new Handler(this, function(pkg:NetPackage):void {
                var city:EntityCity = citys[pkg.receiveData.city];
                city.isDetect = true;
            }));

            NetSocket.instance.on(NetSocket.EVENT_SOCKET_RELOAD, this, this.reload);


            if (ConfigServer.system_simple.online_is) {
                Laya.timer.frameLoop(1, this, this.enterFrame);
                this["my_count"] = 0;
            }

        }

        private function parseAerna():void {
            for (var i:int = 0, len:int = 8; i < len; i++) {
                var arena:EntityArena = new EntityArena();
                arena.index = i;
                arena.setPos();
                this.arenas.push(arena);
            }
            this.changeAerna();
            this.queueFun.next();
        }

        private function changeAerna():void {
            var obj:Object = ModelArena.getArenaData();
            for (var i:int = 0, len:int = 8; i < len; i++) {
                var arena:EntityArena = this.arenas[i];
                arena.initConfig(obj[(i + 1).toString()]);
            }
        }

        private function initAerna():void {
            Trace.log("初始化雕像")
            ModelArena.instance.on(ModelArena.EVENT_UPDATE_ARENA_CLIP, this, this.changeAerna);
            Laya.timer.callLater(this, this.parseAerna);
        }

        private function initNewGuide():void {
            var arr:Array = ModelGuide.getGuideAssets();
            if (arr) {
                LoadeManager.loadImg(arr, Handler.create(this, function():void {
                    queueFun.next();
                }));
            } else {
                queueFun.next();
            }
        }

        private function initSeason():void {
            var modelUser:ModelUser = ModelManager.instance.modelUser;
            var season:int = modelUser.getGameSeason(-1, true); // 季节
            if (ConfigApp.testSeason) {
                var testSeason:Number = Number(ConfigApp.testSeason);
                if (testSeason > -1 && testSeason < 4) {
                    season = testSeason;
                }
            }
            ViewManager.changeSeason(season, Handler.create(this, function():void {
                queueFun.next();
            }));
        }

        private var qizi:Object = {};

        private function initOther():void {
            this.checkQizi(EventConstant.BUFF_CORPS);
            this.checkQizi(EventConstant.BUFF_EXPEDITION);
            ModelManager.instance.modelGame.on(ModelGame.EVENT_BUFFS_ORDER_CORPS_CHANGE, this, function():void {
                checkQizi(EventConstant.BUFF_CORPS);
            });
            ModelManager.instance.modelGame.on(ModelGame.EVENT_BUFFS_EXPEDITION_CHANGE, this, function():void {
                checkQizi(EventConstant.BUFF_EXPEDITION);
            });
            this.queueFun.next();
            this.queueFun = null;

            if (ModelManager.instance.modelUser.getUseFunctionByKey("use_drink") == 1) {
                ModelManager.instance.modelGame.heroDrinkTimer(); //宴请
            } else {
                ModelManager.instance.modelGame.heroCatchTimer(); //切磋
            }
            getFightLogs(true); // 初始化战报
            ModelManager.instance.modelGame.checkPKnpcTimer(1000); //异族入侵
        }

        /**
         * 以前写的旗子太着急了。。。 现在 旗子统一走这里。。。 以前的不管了。放那里吧。
         * @param	type
         */
        private function checkQizi(type:String):void {
            var now:Array = ModelOfficial.getBuff5Arr(type);
            Trace.log("令牌" + type, now);
            this.qizi[type] ||= [];
            var old:Array = this.qizi[type];
            old = old.filter(function(city:String, index:int, arr:Array):Boolean {
                var result:Boolean = ArrayUtils.contains(city, now);
                if (result) {
                    ArrayUtils.remove(city, now);
                } else {
                    EntityCity(citys[city])[type] = false;
                }
                return result;
            });
            for (var i:int = 0, len:int = now.length; i < len; i++) {
                EntityCity(citys[now[i]])[type] = true;
                old.push(now[i]);
            }
            this.qizi[type] = old;
        }

        private function initVisit():void {
            ModelManager.instance.modelGame.once(ModelVisit.EVENT_INIT_VISIT, this, function(e:Event):void {
                queueFun.next();
            });
            ModelManager.instance.modelGame.getCityVisit(); //拜访
        }

        private function initFtask():void {
            //ModelManager.instance.modelGame.getFtaskData();
            ModelManager.instance.modelGame.on(ModelFTask.EVENT_ADD_FTASK, this, function(e:ModelFTask):void {
                EntityCity(citys[parseInt(e.city_id)]).ftask = e;
            });
            ModelManager.instance.modelGame.once(ModelFTask.EVENT_INIT_FTASK, this, function(e:ModelFTask):void {
                for (var name:String in ModelFTask.ftaskModels) {
                    EntityCity(citys[name]).ftask = ModelFTask.ftaskModels[name];
                }
                queueFun.next();
            });
        }

        private function onCheckNpcHandler(e:Array):void {
            var climbs:Object = e;
            for (var name:String in climbs) {
                if (monsters[name])
                    continue;
                var monster:EntityMonster = new EntityMonster();
                monster.initConfig(climbs[name]);
                this.monsters[name] = monster;
            }
        }

        private function onAddHeroCatchHandler(time:Number):void {
            if (ModelManager.instance.modelUser.getUseFunctionByKey("use_drink") == 1) {
                return;
            }

            clearHeroCatch();
            
            var hero_catch:Object = ModelManager.instance.modelUser.hero_catch;
            if (!hero_catch || !hero_catch.hero_list) return;
            for (var i:int = 0, len:int = hero_catch.hero_list.length; i < len; i++) {
                var data:Object = hero_catch.hero_list[i];
                var cityId:int = parseInt(data[1]);
                var heroId:String = data[0].hid;
                this.heroCatch[i] ||= new EntityHeroCatch();
                EntityHeroCatch(this.heroCatch[i]).initConfig({cityId: cityId, heroId: heroId});
            }

            this.event(EventConstant.HERE_CATCH);
        }

        private function onAddHeroDrinkHandler():void {
            if (ModelManager.instance.modelUser.getUseFunctionByKey("use_drink") != 1) {
                return;
            }

            clearHeroCatch();

            var drink:Object = ModelManager.instance.modelUser.drink;
            var len:int = drink.drink_list.length;
            this.heroDrink.splice(len);
            for (var i:int = 0; i < len; i++) {
                var data:Object = drink.drink_list[i];
                var cityId:int = parseInt(data[1]);
                var heroId:String = data[0][0][0]['hid'];
                this.heroDrink[i] ||= new EntityHeroDrink();
                EntityHeroDrink(this.heroDrink[i]).initConfig({cityId: cityId, heroId: heroId});
            }

            this.event(EventConstant.HERO_DRINK);
        }

        public function clearHeroCatch():void {
            var entitys:Array = heroCatch.length > 0 ? heroCatch : heroDrink;
            entitys.forEach(function(e:EntityCityTile):void {
                e.view && Tools.destroy(e.view);
            });
        }

        /**
         *
         * @param	receiveData {city:{cid:1, country:1}}
         */
        public function onFightEndHandler(receiveData:Object, updateData:Boolean = true):void {
            if (updateData)
                ModelOfficial.updateFightEnd(receiveData);
            var cid:* = receiveData.city.cid;
            var city:EntityCity = this.citys[cid];
            var country:int = parseInt(receiveData.city.country);

            var oldCountry:int = city.country;

            if (country != city.country) {
                city.country = country;

                city.isDetect = false;
            } else if (!city.myCountry && city.isDetect) {
                city.isDetect = false;
            }

            var myCountry:int = ModelManager.instance.modelUser.country;
            if (oldCountry === myCountry) {
                if (ModelFTask.ftaskModels.hasOwnProperty(cid + "")) {
                    var mft:ModelFTask = ModelManager.instance.modelGame.getModelFtask(cid + "");
                    if (mft.status == 0) { // 被攻占   移除ftask
                        if(!mft.cityControl || mft.cityControl.hero_id == "") {
                            ModelManager.instance.modelGame.removeFtask(cid);
                        }
                    }
                }
            }


            if (city.myCountry && updateData) { //代表是我的城池。
                var farr:Array = ModelManager.instance.modelUser.ftask[cid + ""];
                if (farr && farr[0] != -1) {
                    ModelManager.instance.modelGame.addFtask(cid + "");
                } else {
                    if (ConfigServer.city[cid].pctask_id) { //该城市有民情才调接口
                        if (ModelManager.instance.modelUser.ftask[cid] && ModelManager.instance.modelUser.ftask[cid][0] == -1) {
                            // trace("========该城市民情已完成。");
                        } else {
                            NetSocket.instance.send("get_ftask", {}, Handler.create(this, function(np:NetPackage):void {
                                var obj:Object = Tools.copyObj(ModelManager.instance.modelUser.ftask);
                                ModelManager.instance.modelUser.updateData(np.receiveData);
                                var newObj:Object = ModelManager.instance.modelUser.ftask;
                                for (var s:String in newObj) {
                                    if (!obj.hasOwnProperty(s)) {
                                        ModelManager.instance.modelGame.addFtask(s);
                                    }
                                }
                                // trace("========重新调用刷新民情的接口。");
                            }));
                        }
                    }
                }

                if (oldCountry != myCountry) {
                    //攻下襄阳城城门
                    if (ConfigServer.city[cid].cityType == ConfigConstant.CITY_TYPE_GATE) {
                        ModelManager.instance.modelCountryPvp.showHeroTalk("500043", [Tools.getMsgById(ConfigServer.city[cid].name)]);
                    }

                    //攻下襄阳城
                    if (ConfigServer.city[cid].cityType == ConfigConstant.CITY_TYPE_DEST) {
                        ModelManager.instance.modelCountryPvp.showHeroTalk("500044");
                    }
                }

            }

            city.fire = false;
            if (oldCountry == myCountry) {
                ModelTask.checkFireCity(2, receiveData);
            }
            
            // 刷新战报
            ModelFightLog_H.instance.getFightLog(receiveData.city);
            getFightLogs();
        }

        public var fightLogs:Array = []; // 保存战报数据
        /** 获取战报 */
        private function getFightLogs(init:Boolean = false):void {
            NetSocket.instance.send(NetMethodCfg.WS_SR_GET_FIGHT_LOG, {}, Handler.create(this, function(re:NetPackage):void {
                var logs:Array = re.receiveData;
                if (!init) {
                    var oldLogIds:Array = fightLogs.map(function(arr:Array):int { return arr[0]; }); // 使用时间戳当作战报id
                    var hasNewLog:Boolean = logs.some(function(arr:Array):Boolean {
                        return oldLogIds.indexOf(arr[0]) === -1; // 有新的战报
                    });
                    hasNewLog && ModelManager.instance.modelGame.event(ModelGame.EVENT_FIGHT_LOG_CHANGE);
                }
                fightLogs = logs;
            }));
        }

        private function onCityFireHandler(vo:NetPackage):void {
            if (!vo) {
                //国战点战鼓堆满消息，此时进入跨服战，加载中有几率报错   zhuda
                console.error('跨服赛报错容错');
                return;
            }
            var city:EntityCity = this.citys[vo.receiveData.city];
            if ("country" in vo.receiveData)
                city.country = vo.receiveData.country;
            city.fire = true;
            city.isDetect = vo.receiveData.fire_country == ModelManager.instance.modelUser.country;

            if (ModelOfficial.checkCityIsMyCountry(vo.receiveData.city)) {
                ModelTask.checkFireCity(1, vo.receiveData);
            }
        }

        public function getCapitalId(country:int):String {
            return ConfigServer.country.country[(country < 0 ? (country + 3) : country)]["capital"];
        }

        public function getCapital(country:int):EntityCity {
            return this.citys[getCapitalId(country)];
        }


        public function get myCapital():EntityCity {
            return this.getCapital(ModelUser.getCountryID());
        }

        public function createMarch(data:*):void {
            if (data.xtype === EntityMarch.TYPE_COUNTRY_ARMY) {
                new CountryArmy().init(data);
            }
            if (data.xtype === EntityMarch.TYPE_PATRIOT_ARMY) {
                new PatriotArmy().init(data);
            }
            if (parseInt(data.status) != ModelTroop.TROOP_STATE_MOVE && parseInt(data.status) != ModelTroop.TROOP_STATE_RECALL)
                return;
            var march:EntityMarch = new EntityMarch(this, -1);
            march.initConfig(data);
            this.removeMarch(march.id); //据说要处理之前的行军。
            this.marchs[march.id] = march;
            this.event(EventConstant.MARCH_CREATE, march);
        }

        public function marchSpeedUp(id:String, data:*):void {
            if (!this.marchs)
                return;
            var march:EntityMarch = this.marchs[id];
            if (!march)
                return;
            march.setRate(data.data.speedup);
        }

        public function removeMarch(id:String):void {
            if (this.marchs[id]) {
                EntityMarch(this.marchs[id]).clear();
                delete this.marchs[id];
            }
        }

        public function getCitys(params:Object):Array {
            var result:Array = [];
            for (var cityId:String in this.citys) {
                var entity:EntityCity = this.citys[cityId];
                for (var name:String in params) {
                    if (entity[name] == params[name]) {
                        result.push(entity);
                        break;
                    }
                }
            }
            return result;
        }


        public function getFilterCitys(fun:Function):Array {
            var result:Array = [];
            for (var cityId:String in this.citys) {
                var entity:EntityCity = this.citys[cityId];
                if (fun(entity)) {
                    result.push(entity);
                }
            }
            return result;
        }

        /**
         * 获取我自己国家城市
         * @param	params 额外的检测。 属性为EntityCity的属性值。
         * @return
         */
        public function getMyCountryCitys(params:Object = null):Array {
            params ||= {};
            params.country = ModelUser.getCountryID();
            return this.getCitys(params);
        }

        public function recallMarch(id:String):void {
            var march:EntityMarch = this.marchs[id];
            if (march) {
                march.recall();
            }
        }

        private function createTroops(receiveData:Object):void {
            var uid:String = '';
            var hid:String = '';
            var data:Object = null;

            var marchDatas:Object = receiveData.troops;
            for (uid in marchDatas) {
                for (hid in marchDatas[uid]) {
                    data = marchDatas[uid][hid];
                    createMarch(data);
                }
            }
            var ng_troops:Object = receiveData.ng_task_troops || {};
            for (uid in ng_troops) { // 新政务中派遣部队
                for (hid in ng_troops[uid]) {
                    data = ObjectUtil.clone(ng_troops[uid][hid], true);

                    // 添加返程数据
                    var city_list:Array = data.data.city_list;
                    var len:int = city_list.length;
                    var back_list:Array = (ObjectUtil.clone(city_list.slice(0, len - 1), true) as Array).reverse();
                    city_list = city_list.concat(back_list);
                    var len2:int = city_list.length;
                    for (var i:int = len - 1; i < len2 - 1; ++i) {
                        city_list[i][1] = city_list[i + 1][1];
                    }
                    city_list[len2 - 1] = [city_list[len2 - 1][0], 0];
                    data.data.city_list = city_list;

                    data.xtype = EntityMarch.TYPE_NG_TASK_ARMY;
                    createMarch(data);
                }
            }
        }

        private function parseData2(receiveData:Object):void {

            this.testMap = receiveData;
            //receiveData = Laya.loader.getRes("testMap.json");
            var cityDatas:Object = receiveData.cities;

            for (var name:String in cityDatas) {
                var city:EntityCity = this.citys[name];
                city.reset(cityDatas[name]);
            }
            createTroops(receiveData);

            //this.checkThief(receiveData.attack_npc);
            //var selfGtaskData:Object = ModelTask.gTask_self_take();
            ////政务。。。
            //for (var gId:String in selfGtaskData) {
            //var gtask:Object = selfGtaskData[gId];
            //var gtaskData:Object = ModelTask.gTask_city_data(gtask.city_id, ModelTask.GTASK_TYPE_GTASK_COLLECT);
            //if (!gtaskData || gtaskData.status < 1) continue;
            //this.addGtask(parseInt(gtask.city_id), ModelItem.getItemIconAssetUI(gtaskData.reward_key), gId);//gtaskData["bot_hero"]["hid"]
            //}
            ////发送后端数据请求地图逻辑
            //AroundManager.instance.init();
        }

        private function parseData(receiveData:Object):void {
            if (TestUtils.isTestShow) {

            }
            this.testMap = receiveData;
            //receiveData = Laya.loader.getRes("testMap.json");
            var cityDatas:Object = receiveData.cities;

            for (var name:String in cityDatas) {
                var city:EntityCity = this.citys[name];
                city && city.setWordData(cityDatas[name]);
            }

            // // 测试护国军
            // var cIds:Array = [10006, 15001, 10008];
            // var speed:int = 20;
            // receiveData.troops['-200020'] = {
            // 	"hero7140": {
            // 		"status": 1,
            // 		"city": cIds[0],
            // 		"xtype": "country_army",
            // 		"hlv": 50,
            // 		"uname": "$500050",
            // 		"num": 16,
            // 		"hid": "hero7140",
            // 		"data": {
            // 		"city_list": cIds.map(function(cid:int, index:int):Array {
            // 			return [cid, cIds.indexOf(index) !== cIds.length - 1? speed:0];
            // 		}),
            // 		"dismiss_time": null,
            // 		"have_dis": 30400,
            // 		"index": 0,
            // 		"start_time": ConfigServer.getServerTimer() / 1000,
            // 		"title": null,
            // 		"type": 0,
            // 		"speedup": 1,
            // 		"last_time": ConfigServer.getServerTimer() / 1000 + 600
            // 		},
            // 		"uid": -200020,
            // 		"car_type": 1,
            // 		"army": [-1, -1],
            // 		"country": 0
            // 	}
            // }
            
            createTroops(receiveData);

            this.checkThief(receiveData.attack_npc);

            var selfGtaskData:Object = ModelTask.gTask_self_take();
            //政务。。。
            for (var gId:String in selfGtaskData) {
                var gtask:Object = selfGtaskData[gId];
                var gtaskData:Object = ModelTask.gTask_city_data(gtask.city_id, ModelTask.GTASK_TYPE_GTASK_COLLECT);
                if (!gtaskData || gtaskData.status < 1)
                    continue;
                this.addGtask(parseInt(gtask.city_id), ModelItem.getItemIconAssetUI(gtaskData.reward_key), gId); //gtaskData["bot_hero"]["hid"]
            }
            //发送后端数据请求地图逻辑
            AroundManager.instance.init();
        }

        private function addGtask(cityId:int, icon:String, id:String):void {
            var gtask:EntityGtask = new EntityGtask();
            gtask.city = this.citys[cityId];
            gtask.icon = icon;
            gtask.name = Tools.getMsgById(id + "_name");
            gtask.city.gtask = gtask;
            gtask.initConfig();
            gtask.city.event(EventConstant.UPDATE_GTASK);
        }

        public function checkThief(attack_npc:Array = null, checkMap:Boolean = true):void {
            var n1:Number = ConfigServer.getServerTimer();
            var n2:Number = 0;
            var n3:Number = 0;
            for (var i:int = this.thief.length - 1; i > -1; i--) {
                var thiefConfig:Object = ConfigServer.getAttackNpcConfig(this.thief[i].type);
                n2 = this.thief[i].start_time * Tools.oneSecondMilli + thiefConfig.speed * Tools.oneSecondMilli;
                if (n1 >= n2) {
                    this.thief.splice(i, 1);
                } else {
                    if (n3 == 0)
                        n3 = n2 - n1;
                    else
                        n3 = n3 > n2 - n1 ? n2 - n1 : n3;
                }

            }
            if (attack_npc) {
                this.thief = this.thief.concat(attack_npc);
                this.event(EventConstant.THIEF, [attack_npc]); //有更新的派发更新的。 空代表派发所有。
                if (attack_npc[0] && attack_npc[0].cid == ModelManager.instance.modelCountryPvp.xyCityId + "") { //襄阳城出现黄巾军
                    ModelManager.instance.modelCountryPvp.showHeroTalk(ModelOfficial.getXYCountryId() == ModelManager.instance.modelUser.country ? "500045" : "500046");
                }
            } else {
                if (checkMap) {
                    this.event(EventConstant.THIEF, [this.thief]); //有更新的派发更新的。 空代表派发所有。
                }
            }
            if (this.thief) {
                ModelTask.npcInfoThiefCheck(this.thief);
            }

            Laya.timer.clear(this, checkThiefUpdate);
            if (n3 > 0) {
                Laya.timer.loop(n3, this, checkThiefUpdate);
            }
        }

        /**
         * 每秒调用 检查黄巾军
         */
        public function checkThiefUpdate():void {
            if (this.thief && this.thief.length > 0) {
                checkThief(null, false);
            } else {
                Laya.timer.clear(this, checkThiefUpdate);
            }
        }

    }
}

package sg.view.init {
    import ui.init.viewMBPayUI;
    import sg.model.ModelMB;
    import laya.events.Event;
    import sg.utils.Tools;
    import laya.display.Sprite;
    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;
    import sg.model.ModelUser;
    import sg.utils.ArrayUtil;
    import sg.net.NetHttp;
    import sg.model.ModelGame;
    import laya.utils.Handler;
    import sg.cfg.VoucherConfig;

    public class ViewMBPay extends viewMBPayUI {
        public var md:ModelMB;
        private var payArg:Array;
        private var httpStatus:Number = -1;
        private var salePid:Boolean = false;

        public function ViewMBPay() {
            this.mbPay.offAll();
            this.normalPay.offAll();
            this.mbPay.on(Event.CLICK, this, this.mbPayClick);
            this.normalPay.on(Event.CLICK, this, this.normalPayClick);
        }

        override public function initData():void {
            this.payArg = this.currArg;
            var payCfg:Object = ConfigServer.pay_config[this.payArg[0]];
            this.comTitle.setViewTitle(VoucherConfig.getText("voucher_title")); // 代金券充值
            this.money.text = VoucherConfig.getText("voucher_money", [payCfg[0]]);
            this.info.text = VoucherConfig.getText("voucher_info");
            this.mbPay.label = VoucherConfig.getText("voucher_pay"); // 代金券支付
            this.normalPay.label = Tools.getMsgById("MB_common");
            this.mb_have.text = VoucherConfig.getText("voucher_have", [ModelManager.instance.modelUser.getVoucherAmount()]);
        }

        private function mbPayClick():void {
            ModelGame.toPay(payArg[0], payArg[1], salePid, Handler.create(this, this.mbPayFunc));
            this.closeSelf();
        }

        //代金券支付方法
        private function mbPayFunc():void {
            var pid:String = this.payArg[1] || payArg[0];
            var user:ModelUser = ModelManager.instance.modelUser;
            var ldt:Number = ConfigServer.getServerTimer();
            var orderId:String = pid + "|" + user.zone + "|" + user.mUID_base + "|" + ldt;
            var param:Object = {order_id: orderId,
                    sessionid: ModelManager.instance.modelUser.mSessionid};
            NetHttp.instance.send("user_zone.voucher_pay", param, Handler.create(this, this.mbPayCallback));
        }

        private function mbPayCallback(data:Object):void {
            this.httpStatus = Number(data["server_status"]);
            if (this.httpStatus == NetHttp.STATUS_SERVER_OK) {
                // 更新用户数据，包括代金券数量
                ModelManager.instance.modelUser.updateData(data);
            }
        }

        private function normalPayClick():void {
            ModelGame.toPay(payArg[0], payArg[1], salePid);
            this.closeSelf();
        }

        override public function onRemoved():void {

        }

    }
}

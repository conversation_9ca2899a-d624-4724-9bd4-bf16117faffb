# -*- coding: utf-8 -*-
import os
import requests
import math
import pymongo
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render_to_response
from django.template import RequestContext
from django.conf import settings
import datetime, time
from game_lib.common import utils
from game_lib.models.main import UserZone, UserDuplicate, BugMsg, AdClick, InstallCode, CountryNotice, CallInterfaceRecord, GatherPlayer, GatherPlayerData, InvitationRecord, IpaddressRecord
import json
from jsonrpc import jsonrpc_method
import jsonrpc
from game_lib.libs.model import Serializer
from game_lib.logics import api_view
from game_lib.logics import game_config, reward_config
from game_lib.models.reward import Rewards, RewardCode
from db.database import pay_records_table, qqgame_confirm_table
from sqlalchemy import *
import cPickle as pickle
import urllib2, urllib
from libs.weixin import WXAPI
from libs.huawei import HuaWei
from libs.huawei_tw import HuaW<PERSON> as HuaWei_tw
from libs.yyb_pay import YybPay
from libs.weixin_app import WeiXinPay
from libs.juedi import JueDi
from libs.weixin_share import WeixinShare
from game_lib.models.cache import CacheTable

share_api = WeixinShare()
from libs.juedi_ios import JueDiIos
from game_lib.models.config import Configs, TestConfigs
from game_lib.logics import config_cache
from game_lib.logics import reward_cache
import hashlib
from game_lib.models.main import FreezeLog
from libs.yiyoujiahe import YiyouJH
from libs.zhifubao_h5 import ZhiFuBaoH5
from db.database import pay_records_table as ASP, mycard_pay_records as mycard
from Crypto.Cipher import AES
import base64
import ipdb
import zlib
from game_lib.models.u37 import u_dict_37
from game_lib.db.database import c_cache as cache
from game_lib.db.database import ShardMetas
from game_lib.db.mongo import get_dbengine
from game_lib.logics import notice_config
from game_lib.logics import notice_cache
from game_lib.models.zone import MergeRecord, Zones
from game_lib.common.lock import Lock
from tornado.httpclient import HTTPClient
from copy import deepcopy as copy

ip_db = ipdb.City(settings.BASEROOT + "/ipiptest.ipdb")

def get_config_pay_money(pid):
    pid = pid.split('_')[0]
    goods_config = game_config.goods.get(pid,None)
    if goods_config:
        pay_id = goods_config['pay_id']
    else:
        pay_id = pid
    pay_money = game_config.pay_config[pay_id][0]
    return pay_money

pay_callback_log = []
def print_pay_callback_params(func):
    def wrapped_func(request, *args, **kwargs):
        try:
            if func.__name__ not in pay_callback_log:
                pay_callback_log.append(func.__name__)
                req_params = {}
                for k,v in request.REQUEST.items():
                    req_params[k] = v
                print 'PF_PAY_CALLBACK_PARAMS - {0} - {1}'.format(func.__name__, req_params)
        except:
            pass
        finally:
            return func(request, *args, **kwargs)
    return wrapped_func

def forward_sh_callback(func):
    """
    转发请求到审核
    """
    def wrapped_func(request, *args, **kwargs):
        try:
            if settings.SUBTEST:
                return func(request, *args, **kwargs)
            if func.__name__ in ['h5_qq_callback', 'h5_changxiang_callback']:
                sh_url = 'http://sg3sh.ptkill.com/%s/' % func.__name__
            elif func.__name__ == 'ea37_callback':
                sh_url = 'http://ea37sh.ptkill.com/ea37_callback/'
            elif func.__name__ == 'tw37_callback':
                sh_url = 'http://tw37sh.ptkill.com/tw37_callback/'
            elif func.__name__ == 'fs_wx_37_callback':
                sh_url = 'https://zsh-ts.akbing.com/fs_wx_37_callback/'
            else:
                sh_url = None
            if sh_url is not None:
                if request.method == 'GET':
                    req_params = {}
                    for k,v in request.REQUEST.items():
                        req_params[k] = v
                    requests.get(url, params=req_params, timeout=1)
                else:
                    content_type = request.META['CONTENT_TYPE']
                    if content_type and ('json' in content_type or 'text' in content_type):
                        req_params = json.loads(request.raw_post_data)
                        requests.post(sh_url, json=req_params,
                                headers={'Content-Type': content_type},
                                timeout=1)
                    else:
                        req_params = {}
                        for k,v in request.REQUEST.items():
                            req_params[k] = v
                        requests.post(sh_url, data=req_params,
                                headers={'Content-Type': content_type},
                                timeout=1)
        except:
            utils.print_err()
        finally:
            return func(request, *args, **kwargs)
    return wrapped_func

def freeze(request):
    uid_params = uid_zone.split('|')
    if len(uid_params) == 3:
        uid, zone, status = uid_params
        hour = -1
    else:
        uid, zone, status, freeze_days = uid_params
        hour = int(freeze_days) * 24
    if game_config.zone[zone][8]:
        old_uid = UserZone.get_old_uid(int(uid), zone)
        old_zone = int(uid) / settings.UIDADD
    else:
        old_uid = uid
        old_zone = zone

    status = int(status)
    user_zone = UserZone.get(int(old_uid))
    server_raise_pf = game_config.system_simple['server_raise_pf'].get(user_zone.pf, None)
    if server_raise_pf:
        server_raise_msg = getattr(game_config, 'server_raise_msg_%s' % server_raise_pf)
    else:
        server_raise_msg = game_config.server_raise_msg
    if status == 1:
        freeze_msg = server_raise_msg['freeze_user']
    elif status == 2:
        freeze_msg = server_raise_msg['freeze_chat']
    FreezeLog.add_freeze_log(old_uid, old_zone, 'freeze_msg', status, -1, 'admin', freeze_msg)
    freeze_dict = {'uid': int(old_uid), 'admin_user': 'admin', 'freeze_status': status, 'hour': hour,
                   'msg': freeze_msg}
    for zone in user_zone.zone_login.keys():
        try:
            UserZone.call_server_api(zone, 'freeze_do', freeze_dict)
        except:
            utils.print_err()
    return True

def freeze_user(uid, zone, admin_user, freeze_status, freeze_msg, hour=None):
    """
    封禁用户
        freeze_status: 0: 解封 1: 禁登录 2: 禁发言
    """
    if hour is None:
        hour = -1
    FreezeLog.add_freeze_log(uid, zone, 'freeze_msg', freeze_status, -1, admin_user, freeze_msg)
    freeze_dict = {
            'uid': int(uid),
            'admin_user': admin_user,
            'freeze_status': freeze_status,
            'hour': hour,
            'msg': freeze_msg
            }
    UserZone.call_server_api(zone, 'freeze_do', freeze_dict)

def zsh_freeze_user(request):
    uid = request.GET['uid']
    pf_key = '%s|fs_wx_37' % uid
    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone_list:
        return HttpResponse(json.dumps({'state': 20004, 'data': {}, 'msg': u'用户不存在'}))
    user_zone = user_zone_list[0]
    from libs.zsh_37 import Api
    api = Api(pf=user_zone.pf)
    zone = request.GET['dsid']
    gid = request.GET['gid']
    treat_type = int(request.GET['treat_type']) #1.禁言2.解禁言 3.踢下线.4.禁登录 5.解登录 6.改角色名
    atime = request.GET['atime']
    sign = request.GET['sign']
    sign_str = '{key}{uid}{gid}{dsid}{treat_type}{atime}'.format(key=api.sign_key, uid=uid, gid=gid, dsid=zone, treat_type=treat_type, atime=atime)
    _sign = hashlib.md5(sign_str).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'state': 10006, 'msg': '签名错误', 'data': {}}))
    if treat_type == 6:
        UserZone.call_server_api(zone, 'force_reset_uname', {'uid': user_zone.uid})
    else:
        server_raise_pf = game_config.system_simple['server_raise_pf'].get(user_zone.pf, None)
        if server_raise_pf:
            server_raise_msg = getattr(game_config, 'server_raise_msg_%s' % server_raise_pf)
        else:
            server_raise_msg = game_config.server_raise_msg
        if treat_type == 1:
            freeze_status = 2
        elif treat_type == 4:
            freeze_status = 1
        elif treat_type in [2, 5]:
            freeze_status = 0

        if freeze_status == 1:
            freeze_msg = server_raise_msg['freeze_user_zsh']
        elif freeze_status == 2:
            freeze_msg = server_raise_msg['freeze_chat_zsh']
        else:
            freeze_msg = ''

        if int(zone) == 0:
            for z in user_zone.zone_login.keys():
                freeze_user(uid, z, 'zsh', freeze_status, freeze_msg)
        else:
            freeze_user(uid, zone, 'zsh', freeze_status, freeze_msg)
    return HttpResponse(json.dumps({'state': 1, 'data': None, 'msg': u'成功'}))

def bugu_freeze_user(request):
    bantype = int(request.REQUEST['bantype'])
    roleid = request.REQUEST['roleid']
    serverid  = request.REQUEST['serverid']
    if bantype == 2:
        uid = UserZone.get_old_uid(roleid, serverid)
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({'code': 101, 'msg': '用户不存在'}))
    else:
        pf_key = '%s|wx_bugu' % roleid
        user_zone_list = list(UserZone.query({'pf_key': pf_key}))
        if not user_zone_list:
            return HttpResponse(json.dumps({'code': 101, 'msg': '用户不存在'}))
        user_zone = user_zone_list[0]
    from libs.bugu import Api
    loginname = request.REQUEST['loginname']
    oper = request.REQUEST['oper']
    freeze_status = int(request.REQUEST['type'])
    bantime = request.REQUEST.get('bantime', 0)
    atime = request.REQUEST['time']
    sign = request.REQUEST['sign']
    sign_key = 'a862dd12e65eae332bf5cbc93e18b1b4'
    sign_str = '{serverid}{oper}{loginname}{atime}{sign_key}'.format(sign_key=sign_key, loginname=loginname, oper=oper, serverid=serverid, atime=atime)
    _sign = hashlib.md5(sign_str).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'code': 100, 'msg': '签名错误'}))
    server_raise_msg = game_config.server_raise_msg

    if freeze_status == 1:
        freeze_msg = server_raise_msg['freeze_user_bugu']
    elif freeze_status == 2:
        freeze_msg = server_raise_msg['freeze_chat_bugu']
    else:
        freeze_msg = ''
    if int(bantime) == 99999999:
        ban_hours = -1
    else:
        ban_hours = int(bantime)*1.0/60/60

    if int(serverid) == 0:
        for z in user_zone.zone_login.keys():
            freeze_user(uid, z, 'bugu', freeze_status, freeze_msg, hour=ban_hours)
    else:
        freeze_user(uid, serverid, 'bugu', freeze_status, freeze_msg, hour=ban_hours)
    return HttpResponse(json.dumps({'code': 1, 'msg': u'成功'}))


def zsh_zone_list(request):
    """
    37最山海区服列表
    """
    pid = request.GET['pid']
    gid = request.GET['gid']
    t = request.GET['time']
    ext = request.GET['ext']
    sign = request.GET['sign']
    from libs.zsh_37 import Api
    api = Api(pf='fs_wx_37')
    _sign = hashlib.md5(t+pid+gid+api.sign_key).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'state': 10006, 'msg': '签名错误', 'data': {}}))
    res = {
        "state": 1,
        "data": _get_zone_list('fs_wx_37'),
        "msg": "成功"
    }
    return HttpResponse(json.dumps(res))

def zsh_role_list(request):
    """
    37最山海获取账号角色列表
    """
    pid = request.GET['pid']
    gid = request.GET['gid']
    uid = request.GET['uid']
    t = request.GET['time']
    sign = request.GET['sign']
    dsid = request.GET.get('dsid')
    from libs.zsh_37 import Api
    api = Api(pf='fs_wx_37')
    if dsid:
        _sign = hashlib.md5(t+pid+gid+dsid+uid+api.sign_key).hexdigest()
    else:
        _sign = hashlib.md5(t+pid+gid+uid+api.sign_key).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'state': 10006, 'msg': '签名错误', 'data': {}}))
    pf_key = '%s|fs_wx_37' % uid
    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone_list:
        return HttpResponse(json.dumps({'state': 20004, 'data': {}, 'msg': u'用户不存在'}))
    user_zone = user_zone_list[0]
    role_list = []
    for zone in user_zone.zone_login.keys():
        if dsid and dsid != zone:
            continue
        data = cache.get('%s_zsh_user_%s' % (user_zone.uid, zone), None)
        if not data:
            app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
            data = {
                'dsid': zone,
                'dsname': game_config.zone[zone][0],
                'drid': user_zone.uid,
                'drname': app_user['uname'],
                'drlv': app_user['home']['building001']['lv'],
                }
            cache.set('%s_zsh_user_%s' % (user_zone.uid, zone), data, 300)
        role_list.append(data)
    res = {
        "state": 1,
        "data": role_list,
        "msg": "成功"
    }
    return HttpResponse(json.dumps(res))


def zsh_role_info(request):
    """
    37最山海获取账号角色信息
    """
    pid = request.GET['pid']
    gid = request.GET['gid']
    uid = request.GET['uid']
    t = request.GET['timestamp']
    dsid = request.GET['partition']
    roleid = request.GET['roleid']
    _type = request.GET['type']
    date = request.GET['date']
    begin = request.GET['begin']
    end = request.GET['end']
    sign = request.GET['sign']
    from libs.zsh_37 import Api
    api = Api(pf='fs_wx_37')
    _sign = hashlib.md5(pid+gid+t+uid+dsid+roleid+_type+api.sign_key).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'state': 10006, 'msg': '签名错误', 'data': {}}))
    pf_key = '%s|fs_wx_37' % uid
    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone_list:
        return HttpResponse(json.dumps({'state': 20004, 'data': {}, 'msg': u'用户不存在'}))
    user_zone = user_zone_list[0]
    app_user = UserZone.call_server_api(dsid, 'get_user', {'uid': user_zone.uid})
    res = {
        "state": 1,
        "data": {
            'dsid': dsid,
            'dsname': game_config.zone[dsid][0],
            'droleid': user_zone.uid,
            'last_login_time': app_user['online_log']['login_time'],
            'level': app_user['home']['building001']['lv'],
            'level_name': app_user['home']['building001']['lv'],
            'fight_value': app_user['power'],
            'role_regtime': int(time.mktime(app_user['add_time'].timetuple()))
            },
        "msg": "成功"
    }
    return HttpResponse(json.dumps(res))



def get_37user(request):
    try:
        account = request.REQUEST.get('account',None)
        zone = request.REQUEST.get('sid',None)
        ts = request.REQUEST.get('time',None)
        key = 'x^i#3!H!bW0lfPJp@M444Jy7CKvGB!$X'
        sign = hashlib.md5(account+zone+ts+key).hexdigest()
        if sign != request.REQUEST.get('sign',None):
            code = -1
            msg = 'Sign Error'
            data = None
        else:
            my_uid = u_dict_37.get(str(account), None)
            if not my_uid:
                pf_key = '%s|h5_37' % account
                user_zone_list = list(UserZone.query({'pf_key': pf_key}))
                user_zone = user_zone_list[0]
                my_uid = user_zone.uid
            data = cache.get('%s_37user_%s' % (my_uid, zone), None)
            if not data:
                app_user = UserZone.call_server_api(zone, 'get_user', {'uid': my_uid})
                data = {
                    'uid': my_uid,
                    'add_time': str(app_user['add_time']),
                    'uname': app_user['uname'],
                    'power': app_user['power'],
                    'lv': app_user['home']['building001']['lv'],
                    'finish_gtask_day': app_user['gtask']['finish_times'],
                    'finish_gtask': app_user['total_records']['finish_gtask'],
                    'year_credit': app_user['year_credit'],
                    'year_build': app_user['year_build'],
                    'year_kill_num': app_user['year_kill_num'],
                    }
                cache.set('%s_37user_%s' % (my_uid, zone), data, 300)
            code = 0
            msg = 'succ'
    except:
        code = 500
        msg = 'Params Error'
        data = None
    return_dict = {
            'code': code,
            'msg': msg,
            'data': data,
            }

    return HttpResponse(json.dumps(return_dict))

def change_uname(request):
    """
    角色名违规调用修改接口，37用
    :param request:
    :return:
    """
    if settings.WHERE == 'cb37':
        key = '(S%;_WF0)GU!JbMjOIhA:a6Bm8.ecnPQ'
    else:
        key = '16Gu!3$K2;hGM!H.Cp52l!JU;vZ86)'
    params = {}
    for k,v in request.GET.items():
        if k in ['actor', 'sign']:
            continue
        params[k] = v
    now_t = time.time()
    sign = request.GET['sign']
    uid = params['actor_id']
    user_zone = UserZone.get(uid)
    if not user_zone:
        return HttpResponse('-4')
    zone = params['sid']
    _time = int(params['time'])
    if now_t - _time > 10*60:
        return HttpResponse('-3')
    sign_str = ''.join('%s=%s' % (k, v) for k,v in sorted(params.iteritems(), key=lambda x: x[0]))
    sign_str += key
    _sign = hashlib.md5(sign_str).hexdigest()
    if sign != _sign:
        return HttpResponse('-2')
    UserZone.call_server_api(zone, 'force_reset_uname', {'uid': user_zone.uid})
    return HttpResponse('1')

def get_ea37_user_roles(request):

    from libs.ea37 import Api
    try:
        req_params = json.loads(request.raw_post_data)
        api = Api()
        if not api.verify_sign(req_params):
            res = {
                'result': 0,
                'msg': 'sign error',
                'data': {}
            }
            return HttpResponse(json.dumps(res))
        pf_uid = req_params['userID']
        zone = req_params['serverID']
        pf_key = '%s|ea37' % pf_uid
        user_zone_list = list(UserZone.query({'pf_key': pf_key}))
        if not user_zone_list:
            res = {
                'result': 0,
                'msg': 'no roles',
                'data': {}
            }
            return HttpResponse(json.dumps(res))
        user_zone = user_zone_list[0]
        uid = user_zone.uid
        if not game_config.zone[zone][1][0]:
            uid = UserZone.get_new_uid(uid, zone)
        data = cache.get('ea37_get_roles_%s_%s' % (uid, zone), None)
        if not data:
            app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
            data = {
                'roleId': uid,
                'roleName': app_user['uname'],
                'roleLevel': app_user['home']['building001']['lv'],
                'roleCoin': app_user['coin']
                }
            cache.set('ea37_get_roles_%s_%s' % (uid, zone), data, 300)
        res = {
            'result': 1,
            'msg': 'success',
            'data': {
                'roleList': [data]
            }
        }
    except:
        utils.print_err()
        res = {
            'result': 0,
            'msg': 'server error',
            'data': {}
        }
    return HttpResponse(json.dumps(res))

def get_tw37_user_roles(request):

    from libs.ea37 import Api
    try:
        req_params = json.loads(request.raw_post_data)
        api = Api(pf='tw37')
        if not api.verify_sign(req_params):
            res = {
                'result': 0,
                'msg': 'sign error',
                'data': {}
            }
            return HttpResponse(json.dumps(res))
        pf_uid = req_params['userID']
        zone = req_params['serverID']
        pf_key = '%s|tw37' % pf_uid
        user_zone_list = list(UserZone.query({'pf_key': pf_key}))
        if not user_zone_list:
            res = {
                'result': 0,
                'msg': 'no roles',
                'data': {}
            }
            return HttpResponse(json.dumps(res))
        user_zone = user_zone_list[0]
        uid = user_zone.uid
        if not game_config.zone[zone][1][0]:
            uid = UserZone.get_new_uid(uid, zone)
        data = cache.get('tw37_get_roles_%s_%s' % (uid, zone), None)
        if not data:
            app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
            data = {
                'roleId': uid,
                'roleName': app_user['uname'],
                'roleLevel': app_user['home']['building001']['lv'],
                'roleCoin': app_user['coin']
                }
            cache.set('tw37_get_roles_%s_%s' % (uid, zone), data, 300)
        res = {
            'result': 1,
            'msg': 'success',
            'data': {
                'roleList': [data]
            }
        }
    except:
        utils.print_err()
        res = {
            'result': 0,
            'msg': 'server error',
            'data': {}
        }
    return HttpResponse(json.dumps(res))

def get_cb_37_kr_user_roles(request):

    from libs.ea37 import Api
    try:
        req_params = json.loads(request.raw_post_data)
        api = Api(pf='cb_37_kr')
        if not api.verify_sign(req_params):
            res = {
                'result': 0,
                'msg': 'sign error',
                'data': {}
            }
            return HttpResponse(json.dumps(res))
        pf_uid = req_params['userID']
        zone = req_params['serverID']
        pf_key = '%s|cb_37_kr' % pf_uid
        user_zone_list = list(UserZone.query({'pf_key': pf_key}))
        if not user_zone_list:
            res = {
                'result': 0,
                'msg': 'no roles',
                'data': {}
            }
            return HttpResponse(json.dumps(res))
        user_zone = user_zone_list[0]
        uid = user_zone.uid
        if not game_config.zone[zone][1][0]:
            uid = UserZone.get_new_uid(uid, zone)
        data = cache.get('cb_37_kr_get_roles_%s_%s' % (uid, zone), None)
        if not data:
            app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
            data = {
                'roleId': uid,
                'roleName': app_user['uname'],
                'roleLevel': app_user['home']['building001']['lv'],
                'roleCoin': app_user['coin']
                }
            cache.set('cb_37_kr_get_roles_%s_%s' % (uid, zone), data, 300)
        res = {
            'result': 1,
            'msg': 'success',
            'data': {
                'roleList': [data]
            }
        }
    except:
        utils.print_err()
        res = {
            'result': 0,
            'msg': 'server error',
            'data': {}
        }
    return HttpResponse(json.dumps(res))

def get_ea37_pay_list(request):
    try:
        from libs.ea37 import Api, EA_PAY_ID_MAPS
        req_params = json.loads(request.raw_post_data)
        uid = int(req_params['roleID'])
        zone = req_params['serverID']
        uid = UserZone.get_old_uid(uid, zone)
        lang = req_params['lang']
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1000, 'msg': 'roleID not existed', 'data': {}}))
        if zone not in game_config.zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1001, 'msg': 'serverID not existed', 'data': {}}))
        api = Api(pf='ea37')
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 1002, 'msg': 'verify sign error', 'data': {}}))
        if lang == 'ko-KR':
            server_raise_msg = game_config.server_raise_msg_kr
            return_msg = game_config.return_msg_kr
        elif lang == 'ja-JP':
            server_raise_msg = game_config.server_raise_msg_ja
            return_msg = game_config.return_msg_ja
        elif lang in ['zh-TW', 'zh-HK', 'zh-MO']:
            server_raise_msg = game_config.server_raise_msg_tw
            return_msg = game_config.return_msg_tw
        elif lang.startswith('en'):
            server_raise_msg = game_config.server_raise_msg_en
            return_msg = game_config.return_msg_en
        else:
            server_raise_msg = game_config.server_raise_msg
            return_msg = game_config.return_msg
        data = {}
        order_num = 1
        for pay_id, value in sorted(game_config.pay_config.iteritems(), key=lambda x:int(x[0][3:])):
            if pay_id not in EA_PAY_ID_MAPS:
                continue
            pay_num = int(pay_id[3:])
            if pay_num not in range(1, 9) and pay_num != 219:
                continue
            pay_desc = server_raise_msg.get(pay_id, 'Gold')
            data[str(order_num)] = {
                'PRODUCT_DESC': pay_desc,
                'LIMIT': 1,
                'PRODUCT_ID': EA_PAY_ID_MAPS[pay_id],
                'CP_PRODUCT_ID': pay_id,
                'REMARK': '%s|%s|%s|%s|%s' % (pay_id, zone, uid, int(time.time()), user_zone.pf)
            }
            order_num += 1
        goods = []
        for goods_id, value in sorted(game_config.goods.iteritems(), key=lambda x:int(x[0][2:])):
            pay_id = value['pay_id']
            if pay_id not in EA_PAY_ID_MAPS:
                continue
            item = {
                'PRODUCT_DESC': return_msg.get(value['info'], value['info']),
                'LIMIT': 1,
                'PRODUCT_ID': EA_PAY_ID_MAPS[pay_id],
                'CP_PRODUCT_ID': goods_id,
                'REMARK': '%s|%s|%s|%s|%s' % (goods_id, zone, uid, int(time.time()), user_zone.pf),
                'money': game_config.pay_config[pay_id][0]
            }
            goods.append(item)
        for item in sorted(goods, key=lambda x:x['money']):
            item.pop('money')
            data[str(order_num)] = item
            order_num += 1
        return HttpResponse(json.dumps({'result': 1, 'msg': 'success', 'data': {'buyList': data}}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 1003, 'msg': 'system error', 'data': {}}))

def verify_ea37_pay(request):

    try:
        from libs.ea37 import Api, EA_PAY_ID_MAPS
        req_params = json.loads(request.raw_post_data)
        uid = int(req_params['roleID'])
        zone = req_params['serverID']
        uid = UserZone.get_old_uid(uid, zone)
        product_id = req_params['productID']
        pay_id = req_params['cpProductID']
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1000, 'msg': 'roleID not existed', 'data': {}}))
        if zone not in game_config.zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1001, 'msg': 'serverID not existed', 'data': {}}))
        api = Api(pf='ea37')
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 1002, 'msg': 'verify sign error', 'data': {}}))
        if pay_id.startswith('gd'):
            pay_config = game_config.goods
        else:
            pay_config = game_config.pay_config
        if pay_id not in pay_config:
            return HttpResponse(json.dumps({'result': 0, 'code': 1003, 'msg': 'cpProductID not existed', 'data': {}}))
        if pay_id.startswith('gd'):
            pf_product_id = EA_PAY_ID_MAPS[game_config.goods[pay_id]['pay_id']]
        else:
            pf_product_id = EA_PAY_ID_MAPS[pay_id]
        if pf_product_id != product_id:
            return HttpResponse(json.dumps({'result': 0, 'code': 1005, 'msg': 'ProductID error', 'data': {}}))
        return HttpResponse(json.dumps({'result': 1, 'msg': 'success', 'data': {'canBuy': 'Y'}}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 1004, 'msg': 'system error', 'data': {}}))

def get_tw37_pay_list(request):
    try:
        from libs.ea37 import Api, TW_PAY_ID_MAPS
        req_params = json.loads(request.raw_post_data)
        uid = int(req_params['roleID'])
        zone = req_params['serverID']
        uid = UserZone.get_old_uid(uid, zone)
        lang = req_params['lang']
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1000, 'msg': 'roleID not existed', 'data': {}}))
        if zone not in game_config.zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1001, 'msg': 'serverID not existed', 'data': {}}))
        api = Api(pf='tw37')
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 1002, 'msg': 'verify sign error', 'data': {}}))
        if lang == 'ko-KR':
            server_raise_msg = game_config.server_raise_msg_kr
            return_msg = game_config.return_msg_kr
        elif lang == 'ja-JP':
            server_raise_msg = game_config.server_raise_msg_ja
            return_msg = game_config.return_msg_ja
        elif lang in ['zh-TW', 'zh-HK', 'zh-MO']:
            server_raise_msg = game_config.server_raise_msg_tw
            return_msg = game_config.return_msg_tw
        elif lang.startswith('en'):
            server_raise_msg = game_config.server_raise_msg_en
            return_msg = game_config.return_msg_en
        else:
            server_raise_msg = game_config.server_raise_msg
            return_msg = game_config.return_msg
        data = {}
        order_num = 1
        for pay_id, value in sorted(game_config.pay_config.iteritems(), key=lambda x:int(x[0][3:])):
            if pay_id not in TW_PAY_ID_MAPS:
                continue
            pay_num = int(pay_id[3:])
            if pay_num not in range(1, 9) and pay_num != 219:
                continue
            pay_desc = server_raise_msg.get(pay_id, 'Gold')
            data[str(order_num)] = {
                'PRODUCT_DESC': pay_desc,
                'LIMIT': 1,
                'PRODUCT_ID': TW_PAY_ID_MAPS[pay_id],
                'CP_PRODUCT_ID': pay_id,
                'REMARK': '%s|%s|%s|%s|%s' % (pay_id, zone, uid, int(time.time()), user_zone.pf)
            }
            order_num += 1
        goods = []
        for goods_id, value in sorted(game_config.goods.iteritems(), key=lambda x:int(x[0][2:])):
            pay_id = value['pay_id']
            if pay_id not in TW_PAY_ID_MAPS:
                continue
            item = {
                'PRODUCT_DESC': return_msg.get(value['info'], value['info']),
                'LIMIT': 1,
                'PRODUCT_ID': TW_PAY_ID_MAPS[pay_id],
                'CP_PRODUCT_ID': goods_id,
                'REMARK': '%s|%s|%s|%s|%s' % (goods_id, zone, uid, int(time.time()), user_zone.pf),
                'money': game_config.pay_config[pay_id][0]
            }
            goods.append(item)
        for item in sorted(goods, key=lambda x:x['money']):
            item.pop('money')
            data[str(order_num)] = item
            order_num += 1
        return HttpResponse(json.dumps({'result': 1, 'msg': 'success', 'data': {'buyList': data}}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 1003, 'msg': 'system error', 'data': {}}))

def verify_tw37_pay(request):
    try:
        from libs.ea37 import Api, TW_PAY_ID_MAPS
        req_params = json.loads(request.raw_post_data)
        uid = int(req_params['roleID'])
        zone = req_params['serverID']
        uid = UserZone.get_old_uid(uid, zone)
        product_id = req_params['productID']
        pay_id = req_params['cpProductID']
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1000, 'msg': 'roleID not existed', 'data': {}}))
        if zone not in game_config.zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1001, 'msg': 'serverID not existed', 'data': {}}))
        api = Api(pf='tw37')
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 1002, 'msg': 'verify sign error', 'data': {}}))
        if pay_id.startswith('gd'):
            pay_config = game_config.goods
        else:
            pay_config = game_config.pay_config
        if pay_id not in pay_config:
            return HttpResponse(json.dumps({'result': 0, 'code': 1003, 'msg': 'cpProductID not existed', 'data': {}}))
        if pay_id.startswith('gd'):
            pf_product_id = TW_PAY_ID_MAPS[game_config.goods[pay_id]['pay_id']]
        else:
            pf_product_id = TW_PAY_ID_MAPS[pay_id]
        if pf_product_id != product_id:
            return HttpResponse(json.dumps({'result': 0, 'code': 1005, 'msg': 'ProductID error', 'data': {}}))
        return HttpResponse(json.dumps({'result': 1, 'msg': 'success', 'data': {'canBuy': 'Y'}}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 1004, 'msg': 'system error', 'data': {}}))

def get_cb_37_kr_pay_list(request):
    try:
        from libs.ea37 import Api, CB_KR_PAY_ID_MAPS
        req_params = json.loads(request.raw_post_data)
        uid = int(req_params['roleID'])
        zone = req_params['serverID']
        uid = UserZone.get_old_uid(uid, zone)
        lang = req_params['lang']
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1000, 'msg': 'roleID not existed', 'data': {}}))
        if zone not in game_config.zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1001, 'msg': 'serverID not existed', 'data': {}}))
        api = Api(pf='cb_37_kr')
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 1002, 'msg': 'verify sign error', 'data': {}}))
        if lang == 'ko-KR':
            server_raise_msg = game_config.server_raise_msg_kr
            return_msg = game_config.return_msg_kr
        elif lang == 'ja-JP':
            server_raise_msg = game_config.server_raise_msg_ja
            return_msg = game_config.return_msg_ja
        elif lang in ['zh-TW', 'zh-HK', 'zh-MO']:
            server_raise_msg = game_config.server_raise_msg_tw
            return_msg = game_config.return_msg_tw
        elif lang.startswith('en'):
            server_raise_msg = game_config.server_raise_msg_en
            return_msg = game_config.return_msg_en
        else:
            server_raise_msg = game_config.server_raise_msg
            return_msg = game_config.return_msg
        data = {}
        order_num = 1
        for pay_id, value in sorted(game_config.pay_config.iteritems(), key=lambda x:int(x[0][3:])):
            if pay_id not in CB_KR_PAY_ID_MAPS:
                continue
            pay_num = int(pay_id[3:])
            if pay_num not in range(1, 9) and pay_num != 219:
                continue
            pay_desc = server_raise_msg.get(pay_id, 'Gold')
            data[str(order_num)] = {
                'PRODUCT_DESC': pay_desc,
                'LIMIT': 1,
                'PRODUCT_ID': CB_KR_PAY_ID_MAPS[pay_id],
                'CP_PRODUCT_ID': pay_id,
                'REMARK': '%s|%s|%s|%s|%s' % (pay_id, zone, uid, int(time.time()), user_zone.pf)
            }
            order_num += 1
        goods = []
        for goods_id, value in sorted(game_config.goods.iteritems(), key=lambda x:int(x[0][2:])):
            pay_id = value['pay_id']
            if pay_id not in CB_KR_PAY_ID_MAPS:
                continue
            item = {
                'PRODUCT_DESC': return_msg.get(value['info'], value['info']),
                'LIMIT': 1,
                'PRODUCT_ID': CB_KR_PAY_ID_MAPS[pay_id],
                'CP_PRODUCT_ID': goods_id,
                'REMARK': '%s|%s|%s|%s|%s' % (goods_id, zone, uid, int(time.time()), user_zone.pf),
                'money': game_config.pay_config[pay_id][0]
            }
            goods.append(item)
        for item in sorted(goods, key=lambda x:x['money']):
            item.pop('money')
            data[str(order_num)] = item
            order_num += 1
        return HttpResponse(json.dumps({'result': 1, 'msg': 'success', 'data': {'buyList': data}}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 1003, 'msg': 'system error', 'data': {}}))

def verify_cb_37_kr_pay(request):

    try:
        from libs.ea37 import Api, CB_KR_PAY_ID_MAPS
        req_params = json.loads(request.raw_post_data)
        uid = int(req_params['roleID'])
        zone = req_params['serverID']
        uid = UserZone.get_old_uid(uid, zone)
        product_id = req_params['productID']
        pay_id = req_params['cpProductID']
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1000, 'msg': 'roleID not existed', 'data': {}}))
        if zone not in game_config.zone:
            return HttpResponse(json.dumps({'result': 0, 'code': 1001, 'msg': 'serverID not existed', 'data': {}}))
        api = Api(pf='cb_37_kr')
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 1002, 'msg': 'verify sign error', 'data': {}}))
        if pay_id.startswith('gd'):
            pay_config = game_config.goods
        else:
            pay_config = game_config.pay_config
        if pay_id not in pay_config:
            return HttpResponse(json.dumps({'result': 0, 'code': 1003, 'msg': 'cpProductID not existed', 'data': {}}))
        if pay_id.startswith('gd'):
            pf_product_id = CB_KR_PAY_ID_MAPS[game_config.goods[pay_id]['pay_id']]
        else:
            pf_product_id = CB_KR_PAY_ID_MAPS[pay_id]
        if pf_product_id != product_id:
            return HttpResponse(json.dumps({'result': 0, 'code': 1005, 'msg': 'ProductID error', 'data': {}}))
        return HttpResponse(json.dumps({'result': 1, 'msg': 'success', 'data': {'canBuy': 'Y'}}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 1004, 'msg': 'system error', 'data': {}}))


def ccc(request):
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))
    if ip_db.find_map(ip, "CN")['country_name'] == u'中国':
        res = 1
    else:
        res = 0
    return_res = HttpResponse(res)
    return_res['Access-Control-Allow-Origin'] = '*'
    return return_res


def get_user_ip(request):
    user_ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                          0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))
    return_res = HttpResponse(user_ip)
    return_res['Access-Control-Allow-Origin'] = '*'
    return return_res


def qq_ban(request):
    params = json.loads(request.raw_post_data)
    content = params['content']
    from libs.q_qq import QApi
    api = QApi()
    data = api.check_msg(content)
    return_res = HttpResponse(data)
    return_res['Access-Control-Allow-Origin'] = '*'
    return return_res

def qqgame_ban(request):
    try:
        params = json.loads(request.raw_post_data)
        openid = params['openid']
        openkey = params['openkey']
        content = params['content']
        isname = params['isname']
        text_list = [{'text_': content, 'text_scene_': 1 if isname else 2}]
        from libs.qqgame import QqGame
        pf = params['pf']
        api = QqGame(pf=pf)
        data = api.uic_filter(openid, openkey, text_list)
        return_res = HttpResponse(json.dumps(data), content_type='application/json;charset=utf-8')
        return_res['Access-Control-Allow-Origin'] = '*'
        return return_res
    except:
        utils.print_err()
        return HttpResponse('error')


def wx_ban(request):
    params = json.loads(request.raw_post_data)

    token = params['token']
    content = params['content']
    req_url = 'https://api.weixin.qq.com/wxa/msg_sec_check?access_token={token}'.format(token=token)
    req = urllib2.Request(req_url, data=json.dumps({'content': content}, ensure_ascii=False).encode('utf-8'),
                          headers={'Content-Type': 'application/json'})
    resp = urllib2.urlopen(req)
    data = resp.read()
    return_res = HttpResponse(data)
    return_res['Access-Control-Allow-Origin'] = '*'
    return return_res

def get_config_version(ck, zone, um, lan):
    if ck in ['return_msg', 'ope_msg'] and lan and lan != 'cn':
        _ck = '%s_%s' % (ck, lan)
    else:
        if zone:
            merge_maps, merge_ploys = um.split("|")
            merge_times = game_config.zone[zone][8]
            if ck == 'ploy':
                try:
                    if int(merge_ploys) == -1:
                        if merge_times:
                            _ck = '%s_merge_%s' % (ck, merge_times)
                        else:
                            _ck = ck
                    else:
                        _ck = '%s_%s' % (ck, merge_ploys)
                except ValueError:
                    _ck = '%s_%s' % (ck, merge_ploys)
            elif ck in ['map', 'city']:
                if int(merge_maps) == -1:
                    _ck = ck
                else:
                    _ck = '%s_%s' % (ck, merge_maps)
            elif ck in ['credit', 'guide', 'bless_hero']:
                if int(merge_maps) == -1:
                    _ck = ck
                else:
                    _ck = '%s_%s' % (ck, 0)
            elif ck == 'climb':
                if int(merge_maps) == -1:
                    if merge_times:
                        _ck = 'climb_new'
                    else:
                        _ck = ck
                else:
                    _ck = 'climb_0'
            else:
                _ck = ck
        else:
            _ck = ck
    return game_config.config_version_dict[_ck]


def get_json_config(request, config_name):
    config_cache.judge()
    notice_cache.judge()
    ck = config_name.split('.')[0]
    lan = request.REQUEST.get('lan', None)
    zone = request.REQUEST.get('zone', None)
    um = request.REQUEST.get('um', None)
    v = request.REQUEST.get('v', None)
    #if ck not in ['notice', 'ploy', 'map', 'city', 'credit', 'guide', 'bless_hero', 'climb'] and v != game_config.config_version_dict[ck]:
    #    print "difference>>>>>>>>", os.getpid(), zone, ck, v, game_config.config_version_dict[ck]
        
    cval = UserZone.get_config_value(ck, zone, lan, um)
    res_data = {
            'monkey_config': cval
            }
    if ck == 'notice':
        res_data['monkey_version'] = notice_config.config_version_dict['notice']
    else:
        res_data['monkey_version'] = get_config_version(ck, zone, um, lan)

    json_res = json.dumps(res_data, default=Serializer.json_default)

    res = HttpResponse(json_res)
    res["Content-Type"] = "application/octet-stream"
    res["Access-Control-Allow-Origin"] = "*"
    # res["Cache-Control"] = "max-age=8640000"
    res["Cache-Control"] = "public"
    expires_time = datetime.datetime(2080, 12, 12, 12, 12)
    res["Expires"] = "%s GMT" % datetime.datetime.ctime(expires_time)
    return res



def new_get_config(client_data):
    """
    老版本，兼容战斗测试
    :param client_data:
    :return:
    """
    config_cache.judge()
    reward_cache.judge()
    notice_cache.judge()
    lan = client_data['params'].get('lan', None)
    from game_lib.logics.game_config import client_config_dict
    config_dict = {}
    for k in client_config_dict:
        if k in ['pf_system_simple', 'client_config', 'help_msg']:
            continue
        cv = game_config.config_version_dict[k]
        if k in ['return_msg', 'ope_msg'] and lan:
            config_dict[k] = '%s/get_json_config/%s.json?v=%s&lan=%s' % (settings.BASE_URL, k, cv, lan)
        else:
            config_dict[k] = '%s/get_json_config/%s.json?v=%s' % (settings.BASE_URL, k, cv)
    ## 更新公告
    config_dict['notice'] = '%s/get_json_config/notice.json?v=%s' % (settings.BASE_URL, notice_config.config_version_dict['notice'])
    ## 战斗测试配置, 朱达加的，只在这个接口返回
    config_dict['fight_test'] = '%s/get_json_config/fight_test.json?v=%s' % (settings.BASE_URL, game_config.config_version_dict['fight_test'])
    return {'config_dict': config_dict}

def new_get_config_v1(client_data):
    """
    新地图版本
    :param client_data:
    :return:
    """
    config_cache.judge()
    reward_cache.judge()
    notice_cache.judge()
    lan = client_data['params'].get('lan', None)
    ## zone传值的时候返回全部配置，否则返回先加载配置
    zone = client_data['params'].get('zone', None)

    config_dict = {}
    um = None
    query = {}
    if zone:
        um = UserZone.call_server_api(zone, 'get_use_merge', {})
        query['um'] = um
    #print query
    for k in game_config.client_config_dict:
        if k in ['pf_system_simple', 'client_config', 'help_msg']:
            continue
        # 加载资源阶段需要获取的配置项（先加载配置）
        if zone is None:
            if k not in game_config.before_client_config_list:
                continue
        query['v'] = get_config_version(k, zone, um, lan)
        if k in ['return_msg', 'ope_msg'] and lan:
            query['lan'] = lan
        config_dict[k] = '%s/get_json_config/%s.json?' % (settings.BASE_URL, k) + urllib.urlencode(query)
    ## 更新公告
    config_dict['notice'] = '%s/get_json_config/notice.json?v=%s' % (settings.BASE_URL, notice_config.config_version_dict['notice'])
    if settings.WHERE in ['ea37', 'fs37', 'wx37', 'local']:
        v = CacheTable.get('ban_words_version', None)
        if not v:
            v = str(int(time.time()))
            CacheTable.set('ban_words_version', v, 60*60*24*30*12*100)
        ban_words_url = settings.BASE_URL + '/static/sensitive/words.txt?v=%s' % v
    else:
        ban_words_url = None

    return {'config_dict': config_dict, 'ban_words_url': ban_words_url}


def refresh_server_config(request):
    try:
        zone = request.REQUEST.get('zone', None)
        pwd = request.REQUEST.get('pwd', None)
        if settings.PWD != pwd:
            raise Exception('request params pwd error')
        config_cache.judge()
        UserZone.push_server_config(zone)
    except:
        utils.print_err()
    return HttpResponse('ok')


def wxh5_pay(request):
    pay_pf = request.REQUEST.get('pay_pf', None)
    order_id = request.REQUEST.get('order_id', None)
    notify_url = '%s/weixin_app_callback/' % settings.BASE_URL
    api = WeiXinPay(notify_url, pay_pf)
    uid, pid, zone, ts = order_id.split('|')
    pay_money = get_config_pay_money(pid)
    user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[
        0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')
    data = api.h5_pay(order_id.encode('utf-8'), pay_money, user_ip, 'test')
    mweb_url = data['mweb_url']
    redirect_url = 'game.ptkill.com://'
    mweb_url = '%s&redirect_url=%s' % (mweb_url, urllib.quote_plus(redirect_url))
    return render_to_response('wxh5_pay.html', {'mweb_url': mweb_url, }, RequestContext(request))


def wx_h5_pay(request, params):
    """
    三国简体微信支付
    """
    pid = params['pid']
    zone = params['zone']
    uid = params['uid']
    pay_pf = params['pf']
    ts = str(int(time.time()))[2:]
    #pay_pf = 'zhangyoulewan'
    #redirect_url = 'game.ptkill.com' # 该域名必须与申请h5支付时提交得授权域名保持一致

    pay_pf = 'youwpz'
    redirect_url = 'game.dianjcy.com'

    #pay_pf = 'dianjcy'
    #redirect_url = 'game.meng52.net'

    order_id = '%s|%s|%s|%s' % (uid, pid, zone, ts)
    user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[
        0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')

    notify_url = '%s/weixin_app_callback/' % settings.BASE_URL
    api = WeiXinPay(notify_url, pay_pf)
    pay_money = get_config_pay_money(pid)
    data = api.h5_pay(order_id.encode('utf-8'), pay_money, user_ip, pay_pf.encode('utf-8'))
    mweb_url = data['mweb_url']
    mweb_url = '%s&redirect_url=%s://' % (mweb_url, urllib.quote_plus(redirect_url))
    headers = {'CLIENT-IP': user_ip, 'X-FORWARDED-FOR': user_ip}
    req = urllib2.Request(mweb_url, headers=headers)
    req.headers['X-FORWARDED-FOR'] = user_ip
    req.headers['CLIENT-IP'] = user_ip
    req.headers['Referer'] = redirect_url
    req_res = urllib2.urlopen(req).read()
    s = req_res.find('weixin://wap/pay?')
    req_res = req_res[s:]
    e = req_res.find('"')
    pay_url = req_res[:e]

    # pay_url = '%s/wxh5_pay/?order_id=%s&pay_pf=%s' % (settings.BASE_URL,order_id,pay_pf)

    return pay_url


def get_yyjh_pay_sign(request, params):
    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')
    pay_money = get_config_pay_money(pid)
    if int(pay_money) != params['amt']:
        raise Exception('yyjh pay amount error')
    res = YiyouJH().get_pay_code(params)

    return res


def get_yyjh2_pay_sign(request, params):
    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')
    pay_money = get_config_pay_money(pid)
    if int(pay_money) != params['amt']:
        raise Exception('yyjh2 pay amount error')
    res = YiyouJH(tap=1).get_pay_code(params)

    return res

def get_yyjh_hk_pay_sign(request, params):
    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')
    pay_money = get_config_pay_money(pid)
    if int(pay_money) != params['amt']:
        raise Exception('yyjh_hk pay amount error')
    res = YiyouJH(tap=3).get_pay_code(params)

    return res
def get_cb_h5_yyjh_pay_sign(request, params):
    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')
    pay_money = get_config_pay_money(pid)
    if int(pay_money) != params['amt']:
        raise Exception('yyjh pay amount error')
    res = YiyouJH(tap=4).get_pay_code(params)

    return res
def get_cb_h5_yyjh_cn_pay_sign(request, params):
    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')
    pay_money = get_config_pay_money(pid)
    if int(pay_money) != params['amt']:
        raise Exception('yyjh pay amount error')
    res = YiyouJH(tap=5).get_pay_code(params)

    return res

def h5_dqd_callback(request):
    try:
        req_params = {}
        for k,v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_dqd import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('sign_error')
        order_id = req_params['out_order_no']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'success', req_params['price'], 'h5_dqd',
                            req_params['order_no'], 'h5_dqd', 'h5_dqd')
    except:
        utils.print_err()
        return HttpResponse('fail')

@print_pay_callback_params
def h5_panbao_callback(request):
    try:
        req_params = json.loads(request.raw_post_data)
        pay_result = req_params['result']
        if int(pay_result) != 0:
            return HttpResponse('pay_result_error')

        from libs.h5_panbao_liaobe import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('sign_error')
        order_id = req_params['cpOrderId']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', req_params['amount'], 'h5_panbao',
                            req_params['cpOrderId'], 'h5_panbao', 'h5_panbao')
    except:
        utils.print_err()
        return HttpResponse('error')


@print_pay_callback_params
def ad_panbao_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v

        from libs.panbao import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('FAIL')
        order_id = req_params['extra']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', req_params['price'], 'ad_panbao',
                            req_params['orderId'], 'ad_panbao', 'ad_panbao')
    except:
        utils.print_err()
        return HttpResponse('FAIL')

@print_pay_callback_params
def ios_panbao_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v

        from libs.panbao import Api
        api = Api(pf='ios_panbao')
        if not api.check_pay(req_params):
            return HttpResponse('FAIL')
        order_id = req_params['extra']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', req_params['price'], 'ios_panbao',
                            req_params['orderId'], 'ios_panbao', 'ios_panbao')
    except:
        utils.print_err()
        return HttpResponse('FAIL')

def h5_qianqi_callback(request):
    try:
        from libs.qianqi import Api
        req_params = {}
        for k,v in request.REQUEST.items():
            req_params[k] = v
        if req_params['status'] != 'success':
            return HttpResponse('status error')
        api = Api(pf='h5_qianqi')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('error')
        order_id = req_params['orderid']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'success', req_params['money'], 'h5_qianqi', req_params['transnum'], 'h5_qianqi',
                            'h5_qianqi')
    except:
        utils.print_err()
        return HttpResponse('error')

def h5_qianguo_callback(request):
    try:
        from libs.qianqi import Api
        req_params = {}
        for k,v in request.REQUEST.items():
            req_params[k] = v
        if req_params['status'] != 'success':
            return HttpResponse('status error')
        api = Api(pf='h5_qianguo')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('error')
        order_id = req_params['orderid']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'success', req_params['money'], 'h5_qianguo', req_params['transnum'], 'h5_qianguo',
                            'h5_qianguo')
    except:
        utils.print_err()
        return HttpResponse('error')

def ad_6kw_callback(request):
    sign = request.META.get('HTTP_AUTH6KW')
    json_params = request.raw_post_data

    try:
        from libs.ad_6kw import Api
        api = Api()
        if not api.check_pay(sign, json_params):
            return HttpResponse("ERROR")
        params = json.loads(json_params)
        order_id = params['gameOrder']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', params['total'], 'ad_6kw', params['orderID'], 'ad_6kw',
                            'ad_6kw')
    except:
        utils.print_err()
        return HttpResponse('ERROR')


def h5_6kw_callback(request):
    try:
        json_params = request.REQUEST['data']
        sign = request.REQUEST['sign']

        params = json.loads(json_params)

        from libs.h5_6kw import Api
        api = Api()
        if not api.check_pay(sign, params):
            return HttpResponse("ERROR")
        order_id = params['cpOrder']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(params['money'])) * 100, 'h5_6kw',
                            params['orderId'], 'h5_6kw', 'h5_6kw')
    except:
        utils.print_err()
        return HttpResponse('ERROR')

def h5_6kw2_callback(request):
    try:
        json_params = request.REQUEST['data']
        sign = request.REQUEST['sign']

        params = json.loads(json_params)

        from libs.h5_6kw import Api
        api = Api(pf='h5_6kw2')
        if not api.check_pay(sign, params):
            return HttpResponse("ERROR")
        order_id = params['cpOrder']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(params['money'])) * 100, 'h5_6kw2',
                            params['orderId'], 'h5_6kw2', 'h5_6kw2 ')
    except:
        utils.print_err()
        return HttpResponse('ERROR')

def h5_6kw3_callback(request):
    try:
        json_params = request.REQUEST['data']
        sign = request.REQUEST['sign']

        params = json.loads(json_params)

        from libs.h5_6kw import Api
        api = Api(pf='h5_6kw3')
        if not api.check_pay(sign, params):
            return HttpResponse("ERROR")
        order_id = params['cpOrder']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(params['money'])) * 100, 'h5_6kw3',
                            params['orderId'], 'h5_6kw3', 'h5_6kw3')
    except:
        utils.print_err()
        return HttpResponse('ERROR')


def h5_changwan_callback(request):
    res_info = {'code': 200, 'msg': u'成功', 'data': []}
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v

        from libs.h5_changwan import Api
        api = Api()
        if not api.check_pay(req_params):
            res_info['code'] = 202
            res_info['msg'] = u'签名验证失败'
            return HttpResponse(json.dumps(res_info))
        order_id = req_params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, json.dumps(res_info), req_params['pay_amt'], 'h5_changwan',
                            req_params['orderno'], 'h5_changwan', 'h5_changwan')
    except:
        utils.print_err()
        return HttpResponse(json.dumps(res_info))


def ad_360_callback(request):
    """
    360安卓
    :param request:
    :return:
    """

    res_info = {'status': 'ok', 'delivery': 'success', 'msg': ''}
    try:
        if request.REQUEST['gateway_flag'] != 'success':
            res_info['delivery'] = 'other'
            res_info['msg'] = 'gateway_flag not success'
            return HttpResponse(json.dumps(res_info))
        sign_params = {}
        for k, v in request.REQUEST.items():
            if k in ['sign_return', 'sign']:
                continue
            sign_params[k] = v

        from libs.ad_360 import Api

        api = Api()
        delivery, msg = api.check_pay(request.REQUEST.get('sign'), request.REQUEST.get('sign_return'), sign_params)
        res_info['delivery'] = delivery
        res_info['msg'] = msg
        order_id = sign_params['app_order_id']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, json.dumps(res_info), 'no_check', 'ad_360',
                            sign_params['order_id'], 'ad_360', 'ad_360')
    except:
        utils.print_err()
        return HttpResponse(json.dumps(res_info))


def yx7477_callback(request):
    try:
        sign_params = {}
        for k, v in request.REQUEST.items():
            if k in ['param', 'sign']:
                continue
            sign_params[k] = v
        from libs.yx7477 import Api

        api = Api()
        if not api.check_pay_sign(request.REQUEST.get('sign'), sign_params):
            return HttpResponse('error')
        order_id = sign_params['out_trade_no']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 'success'
        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(sign_params['pay_money'])) * 100, 'yx7477', sign_params['order_id'],
                            'yx7477', 'yx7477')
    except:
        utils.print_err()
        return HttpResponse('error')


def yx7477_1_callback(request):
    try:
        sign_params = {}
        for k, v in request.REQUEST.items():
            if k in ['param', 'sign']:
                continue
            sign_params[k] = v
        from libs.yx7477 import Api

        api = Api(pf='yx7477_1')
        if not api.check_pay_sign(request.REQUEST.get('sign'), sign_params):
            return HttpResponse('error')
        order_id = sign_params['out_trade_no']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 'success'
        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(sign_params['pay_money'])) * 100, 'yx7477_1', sign_params['order_id'],
                            'yx7477_1', 'yx7477_1')
    except:
        utils.print_err()
        return HttpResponse('error')

def h5_7477_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            if k in ['param']:
                continue
            req_params[k] = v
        from libs.h5_7477 import Api

        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('error')
        order_id = req_params['out_orderid']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 1
        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['money'])) * 100, 'h5_7477', req_params['order_id'],
                            'h5_7477', 'h5_7477')
    except:
        utils.print_err()
        return HttpResponse(-1)

def get_h5_7477_sign(request, params):
    sign_type = params['sign_type']
    params = params['data']
    from libs.h5_7477 import Api
    api = Api()
    sign = api.get_sign(sign_type, params)
    return {'sign': sign}


def you77_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v

        from libs.you77 import Api
        api = Api()

        if not api.check_pay_sign(req_params):
            return HttpResponse('error')
        order_id = req_params['cp_order']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 'success'

        return add_user_pay(order_id, uid, zone, pid, succ_info, 'no_check', 'you77', req_params['qqes_order'], 'you77',
                            'you77')
    except:
        utils.print_err()
        return HttpResponse('error')


def you77_h5_jp_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            if k == 'attach':
                continue
            req_params[k] = v

        from libs.h5_you77 import Api
        api = Api()

        if not api.check_pay_sign(req_params):
            return HttpResponse('sign error')
        order_id = req_params['cp_order']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 'success'

        return add_user_pay(order_id, uid, zone, pid, succ_info, 'no_check', 'you77_h5_jp', req_params['qqes_order'], 'you77_h5_jp',
                            'you77_h5_jp')
    except:
        utils.print_err()
        return HttpResponse('error')

def get_you77_h5_jp_sign(request, params):
    from libs.h5_you77 import Api
    api = Api()
    sign_type = params.pop('sign_type')
    pid, zone, uid, ts = params['order'].split('|')
    if pid.startswith('gd'):
        pay_id = game_config.goods[pid]['pay_id']
    else:
        pay_id = pid
    pay_money = float(game_config.system_simple['pay_number'].get('you77_h5_jp', {}).get(pay_id))
    if pay_money != params['fee']:
        raise Exception('you77_h5_jp pay money error')
    sign = api.get_sign(params)

    return {'sign': sign}

def you77_create_order(request):
    """
    77游人工创建订单
    :param request:
    :return:
    """
    try:
        from libs.you77 import Api, PID_AD_MPAS
        ext = request.POST['ext']
        pid = PID_AD_MPAS.get(ext, None)
        if pid not in game_config.pay_config:
            return HttpResponse(json.dumps({'msg': '道具ID不合法', 'code': -1}))
        sig_params = {}
        for k, v in request.POST.items():
            if k == 'ext':
                continue
            sig_params[k] = v

        api = Api()
        if not api.check_pay_sign(sig_params):
            return HttpResponse(json.dumps({'msg': '签名验证失败', 'code': -2}))
        uid = sig_params['role_id']
        zone = sig_params['server_id']
        order_id = '%s|%s|%s|%s' % (pid, zone, uid, int(time.time()))
        res_data = {
            "cporder": order_id,
            "goodsname": "Gold",
            "ext": order_id
        }
        res_data['sign'] = api.get_sign(res_data)
        res = {
            "msg": "",
            "code": 1,
            "data": res_data
        }

        return HttpResponse(json.dumps(res))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'msg': 'error', 'code': -3}))


def h5_shouqu_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v

        from libs.h5_shouqu import Api
        api = Api()

        if not api.check_pay_sign(req_params):
            return HttpResponse('error')
        order_id = urllib.unquote(req_params['exInfo'])
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 'success'

        return add_user_pay(order_id, uid, zone, pid, succ_info, req_params['payMoney'], 'h5_shouqu',
                            req_params['orderId'], 'h5_shouqu', 'h5_shouqu')
    except:
        utils.print_err()
        return HttpResponse('error')


def h5_muzhi_callback(request):
    try:
        content = request.REQUEST.get('content')
        sign = request.REQUEST.get('sign')
        content = base64.b64decode(content)
        from libs.h5_muzhi import Api
        api = Api()
        sign_str = content.encode('utf-8') + '&key=' + api.app_key

        if sign != hashlib.md5(sign_str).hexdigest():
            return HttpResponse('success')
        req_params = json.loads(content)
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 'success'

        return add_user_pay(order_id, uid, zone, pid, succ_info, req_params['amount'], req_params['user_id'],
                            req_params['pay_no'], 'h5_muzhi', 'h5_muzhi')
    except:
        utils.print_err()
        return HttpResponse('success')


def h5_muzhi2_callback(request):
    try:
        content = request.REQUEST.get('content')
        sign = request.REQUEST.get('sign')
        content = base64.b64decode(content)
        from libs.h5_muzhi import Api
        api = Api(pf='h5_muzhi2')
        sign_str = content.encode('utf-8') + '&key=' + api.app_key

        if sign != hashlib.md5(sign_str).hexdigest():
            return HttpResponse('success')
        req_params = json.loads(content)
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = 'success'

        return add_user_pay(order_id, uid, zone, pid, succ_info, 'no_check', req_params['user_id'],
                            req_params['pay_no'], 'h5_muzhi2', 'h5_muzhi2')
    except:
        utils.print_err()
        return HttpResponse('success')


def yyjh_callback(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        api = YiyouJH()
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100, req_params['userid'], req_params['order'],
                            'h5_yyjh', 'h5_yyjh')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 0}')


def yyjh2_callback(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        api = YiyouJH(tap=1)
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100, req_params['userid'], req_params['order'],
                            'h5_yyjh2', 'h5_yyjh2')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 0}')

def h5_yyjh_hk_callback(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        api = YiyouJH(tap=3)
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100,
                            req_params['userid'], req_params['order'],
                            'h5_yyjh_hk', 'h5_yyjh_hk')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 0}')

def yyjh_create_order(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        sign = req_params['Sign']
        api = YiyouJH(tap=3)
        sign_str = '{Roleid}{Account}{Sid}{Goodsid}{Money}{Coins}{Time}{app_secret}'.format(app_secret=api.app_secret, **req_params)
        _sign = hashlib.md5(sign_str).hexdigest()
        if sign != _sign:
            return HttpResponse(json.dumps({'Result': 1, 'Payorderid': ''}))
        pid = req_params['Goodsid']
        uid = req_params['Roleid']
        zone = req_params['Sid']
        money = req_params['Money']
        uid = UserZone.get_old_uid(uid, zone)
        user = UserZone.get(uid)
        if not user:
            return HttpResponse(json.dumps({'Result': 1, 'Payorderid': ''}))
        if pid not in game_config.pay_config:
            return HttpResponse(json.dumps({'Result': 1, 'Payorderid': ''}))
        if zone not in game_config.zone:
            return HttpResponse(json.dumps({'Result': 1, 'Payorderid': ''}))
        if money != game_config.system_simple['pay_money']['h5_yyjh_hk_1'].get(pid):
            return HttpResponse(json.dumps({'Result': 1, 'Payorderid': ''}))
        order_id = '%s|%s|%s|%s' % (pid, zone, uid, int(time.time()))
        return HttpResponse(json.dumps({'Result': 0, 'Payorderid': order_id}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'Result': 1, 'Payorderid': ''}))

def h5_7k_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_7k import Api7k
        api = Api7k()
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100, req_params['userid'],
                            req_params['7korder'], 'h5_7k', 'h5_7k')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 0}')


def zfb_h5_pay(request, params):
    pid = params['pid']
    zone = params['zone']
    uid = params['uid']
    pay_pf = params['pf']  #aipinglewan 三国用，  aipinglewan1  赤壁级以后新游戏使用
    ts = str(int(time.time()))

    order_id = '%s|%s|%s|%s' % (uid, pid, zone, ts)
    pay_money = get_config_pay_money(pid)
    notify_url = '%s/zfb_h5_callback/' % settings.BASE_URL
    #pay_url = ZhiFuBaoH5('aipinglewan1').get_h5_pay_url(order_id, pay_money, notify_url)
    #pay_url = ZhiFuBaoH5('zhangyoulewan').get_h5_pay_url(order_id, pay_money, notify_url)
    pay_url = ZhiFuBaoH5('youwpz').get_h5_pay_url(order_id, pay_money, notify_url)
    return pay_url


def zfb_h5_callback(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        if req_params['trade_status'] not in ['TRADE_FINISHED','TRADE_SUCCESS']:
            return HttpResponse('success')
        #pay_pf = req_params.get('passback_params', 'aipinglewan1')
        #pay_pf = req_params.get('passback_params', 'zhangyoulewan')
        pay_pf = req_params.get('passback_params', 'youwpz')

        api = ZhiFuBaoH5(app=pay_pf)
        sign = req_params['sign']
        del req_params['sign']
        del req_params['sign_type']
        if not api.pay_callback_verify(req_params, sign):
            return HttpResponse('error')
        order_id = req_params['out_trade_no']
        pay_other = req_params.get('buyer_logon_id', 'zfb')
        uid, pid, zone, ts = order_id.split('|')
        succ_info = 'success'

        return add_user_pay(order_id, uid, zone, pid, succ_info, 'no_check', pay_other, req_params['trade_no'], 'zfb',
                            'zfb')
    except:
        utils.print_err()
        return HttpResponse('error')


def gdt_click(request):
    return HttpResponse('{"ret": 0, "msg": "succ"}')
    try:
        imei = request.GET['muid'].lower()
        callback_url = request.GET['click_id']

        ad = AdClick.get(imei)
        if not ad:
            ad = AdClick(imei)
            ad.callback_url = callback_url
            ad.status = 0
            ad.pf = 'gdt'
            ad.save()
    except:
        utils.print_err()

    return HttpResponse('{"ret": 0, "msg": "succ"}')

def wxjsp(request):
    ok = request.REQUEST.get('ok')
    appid = WeiXinPay('', '').appid
    redirect_uri = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s/wxjspay&response_type=code&scope=snsapi_base&state=%s#wechat_redirect' % (appid, settings.BASE_URL, ok)
    return HttpResponseRedirect(redirect_uri)


def wxjspay(request):
    try:
        from game_lib.db.database import c_cache as cache
        order_key = request.REQUEST.get('state')
        code = request.REQUEST.get('code')
        order_dict = cache.get(settings.CACHE_PRE + order_key)

        order_id = order_dict['order_id']
        pids = order_dict['pids']
        pay_money = order_dict['pay_money']

        user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[
            0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')
        notify_url = '%s/weixin_sale_callback/' % settings.BASE_URL

        api = WeiXinPay(notify_url, 'wx_axmin')
        openid = api.get_openid(code)
        data = api.js_pay(str(order_id), pay_money, user_ip, pids, openid)
        return render_to_response('weixin_pay.html', {'client_data': data, }, RequestContext(request))
    except:
        utils.print_err()
        return HttpResponse('<h1>无效的二维码，如有问题，请联系客服QQ：3296420063</h1>')




def share(request):
    return render_to_response('share_wap.html', {}, RequestContext(request))


def zone_start_date(request):
    zone = request.REQUEST.get('zone', None)
    zone_date = game_config.zone[zone][2].date()
    return HttpResponse(str(zone_date))


def Ip(request):
    ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[
        0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')
    return HttpResponse(ip)


def Country(request):
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))
    res = ip_db.find_map(ip, "CN")['country_name'] == u'中国'
    return HttpResponse(res)


def yyb_install_from(request, params):
    return {'install_from': 'yyb'}
    pf = 'yyb2'
    yyb2_www_install = UserZone.count({'pf': pf, 'www': 1})
    yyb2_all_install = UserZone.count({'pf': pf})
    install_from = 'yyb'
    if yyb2_all_install:
        if (float(yyb2_www_install) / float(yyb2_all_install)) < 0.1:
            install_from = 'test'

    return {'install_from': install_from}


def yyb_pay_from(request, params):
    return {'pay_from': 'yyb'}


def get_weixin_share(request, params):
    pf = params['pf']
    url = game_config.system_simple['share_pf'][pf][4]
    share_dict = share_api.get_share_dict(url)
    return share_dict


def juedi_callback(request):
    req_params = {}
    for k, v in request.GET.items():
        if not v:
            continue
        req_params[k] = v
    sign = req_params['sign']
    del req_params['sign']
    api = JueDi()
    if sign != api.get_md5sig(req_params, key_type='pay'):
        return HttpResponse('SIGNERROR')
    order_id = req_params['game_trade_no']
    pid, zone, uid, pf, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', 'no_check', req_params['channel_trade_no'],
                        req_params['trade_no'], 'juedi', 'juedi')


def juedi_ios_callback(request):
    req_params = {}
    for k, v in request.GET.items():
        if not v:
            continue
        req_params[k] = v
    sign = req_params['sign']
    del req_params['sign']
    api = JueDiIos()
    if sign != api.get_md5sig(req_params, key_type='pay'):
        return HttpResponse('SIGNERROR')
    order_id = req_params['game_trade_no']
    pid, zone, uid, pf, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', 'no_check', req_params['channel_trade_no'],
                        req_params['trade_no'], 'juedi_ios', 'juedi_ios')


def weixin_sale_callback(request):
    req_params = {}
    xml_data = request.raw_post_data
    notify_url = '%s/weixin_sale_callback/' % settings.BASE_URL
    req_params = WeiXinPay.get_params_from_xml(xml_data)
    order_id = req_params['out_trade_no']
    uid, zone, ts = order_id.split('|')
    sign = req_params['sign']
    del req_params['sign']
    api = WeiXinPay(notify_url, 'wx_admin')
    local_sign = api.get_md5sign(req_params)
    pay_money = float(req_params['total_fee']) / 100.0
    pay_sale = [pay_money, req_params['attach']]

    succ_info = """
    <xml>
    <return_code><![CDATA[SUCCESS]]></return_code>
    <return_msg><![CDATA[OK]]></return_msg>
    </xml>
    """
    err_info = """
    <xml>
    <return_code><![CDATA[FAIL]]></return_code>
    <return_msg><![CDATA[SIGNERROR]]></return_msg>
    </xml>
    """

    if local_sign != sign:
        return HttpResponse(err_info)

    return add_user_pay(order_id, uid, zone, pay_sale, succ_info, 'no_check', 'weixin_sale',
                        req_params['transaction_id'], 'weixin_sale', 'weixin_sale')


def weixin_app_callback(request):
    req_params = {}
    xml_data = request.raw_post_data
    notify_url = '%s/weixin_app_callback/' % settings.BASE_URL
    req_params = WeiXinPay.get_params_from_xml(xml_data)
    order_id = req_params['out_trade_no']
    uid, pid, zone, ts = order_id.split('|')
    sign = req_params['sign']
    del req_params['sign']
    pay_pf = req_params['attach']
    api = WeiXinPay(notify_url, pay_pf)
    local_sign = api.get_md5sign(req_params)

    succ_info = """
    <xml>
    <return_code><![CDATA[SUCCESS]]></return_code>
    <return_msg><![CDATA[OK]]></return_msg>
    </xml>
    """
    err_info = """
    <xml>
    <return_code><![CDATA[FAIL]]></return_code>
    <return_msg><![CDATA[SIGNERROR]]></return_msg>
    </xml>
    """

    if local_sign != sign:
        return HttpResponse(err_info)

    return add_user_pay(order_id, uid, zone, pid, succ_info, req_params['total_fee'], 'weixin_app', req_params['transaction_id'],
                        'weixin_app', 'weixin_app')


def vivo_callback(request):
    from libs.vivo import Vivo
    req_params = {}
    for k, v in request.POST.items():
        if k not in ['signMethod', 'signature']:
            val = v.encode('utf-8')
            kk = k.encode('utf-8')
            req_params[kk] = val
    sign = request.POST.get('signature')
    api = Vivo()
    local_sign = api.get_md5sig(req_params)
    if sign != local_sign:
        return HttpResponse('fail')

    order_id = req_params['cpOrderNumber']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'success', req_params['orderAmount'], 'vivo',
                        req_params['orderNumber'], 'vivo', 'vivo')


def oppo_callback(request):
    from libs.oppo import Oppo

    req_params = {}
    for k, v in request.POST.items():
        if k not in ['sign']:
            val = v.encode('utf-8')
            kk = k.encode('utf-8')
            req_params[kk] = val

    sign = request.POST.get('sign')

    api = Oppo()

    if not api.validate_purchase(req_params, sign):
        return HttpResponse('fail')

    order_id = req_params['partnerOrder']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'result=OK', req_params['price'], 'oppo', req_params['notifyId'], 'oppo',
                        'oppo')


def yqwb_callback(request):
    from libs.yqwb import API

    tradedata = json.loads(request.POST.get('tradedata'))
    sign = request.POST.get('sign')

    if not API.check_pay(tradedata, sign):
        return HttpResponse('failed')

    order_id = tradedata['cptradeno']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'yqwb', tradedata['tradeno'], 'yqwb', 'yqwb')


def mi_callback(request):
    from libs.xiaomi import XiaoMi
    api = XiaoMi()

    req_params = {}
    for k, v in request.REQUEST.items():
        if k not in ['signature']:
            val = v.encode('utf-8')
            kk = k.encode('utf-8')
            req_params[kk] = val
    sign = request.REQUEST.get('signature')

    if not api.check_pay(req_params, sign):
        return HttpResponse('{"errcode":1525}')

    order_id = req_params['cpOrderId']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '{"errcode":200}', req_params['payFee'], 'mi', req_params['orderId'],
                        'mi', 'mi')


def mz_callback(request):
    from libs.meizu import MeiZu
    api = MeiZu()

    req_params = {}
    for k, v in request.REQUEST.items():
        if k not in ['sign', 'sign_type']:
            val = v.encode('utf-8')
            kk = k.encode('utf-8')
            req_params[kk] = val

    sign = request.REQUEST.get('sign')

    if api._hmac_sha1_sig(req_params) != sign:
        return HttpResponse('{"code":1525}')

    order_id = req_params['cp_order_id']
    user_info = req_params['user_info']
    pid, zone, uid, ts = user_info.split('|')

    return add_user_pay(order_id, uid, zone, pid, '{"code":200}', 'no_check', 'mz', req_params['order_id'], 'mz', 'mz')


def uc_callback(request):
    from libs.qyou import Api
    api = Api()

    req_params = json.loads(request.raw_post_data)
    sign = req_params['sign']
    req_params = req_params['data']

    # {u'orderId': u'20190214145106103851', u'cpOrderId': u'********|*************', u'gameId': u'1069523', u'creator': u'ALI', u'failedDesc': u'', u'payWay': u'999', u'amount': u'1.00', u'orderStatus': u'S', u'callbackInfo': u'pay0|2|********|*************', u'accountId': u'dd7e37a9a8d4fdff1547e6dd8d8055c1'}

    if api._hmac_sha1_sig(req_params) != sign:
        return HttpResponse('FAILURE')

    order_id = req_params['callbackInfo']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['amount'])) * 100, 'uc', req_params['orderId'], 'uc', 'uc')


def h5_leyou_callback(request):
    from libs.leyou import LeYou
    api = LeYou()

    params = {}
    for k, v in request.REQUEST.items():
        if v:
            params[k] = v

    if api.pay_sign(params) != params['sign']:
        return HttpResponse('{"result":0}')

    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '{"result":1}', int(float(params['amt'])) * 100, 'h5_leyou', params['order'], 'h5_leyou',
                        'h5_leyou')


def wende_callback_ad(request):
    from libs.wende import WenDe
    api = WenDe()

    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    if int(params['pay_status']) != 1:
        return HttpResponse('error')

    if api.pay_sign_ad(params) != params['sign']:
        return HttpResponse('error')

    order_id = params['extend']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'wende_ad', params['out_trade_no'], 'wende_ad',
                        'wende_ad')


def wende_callback_ios(request):
    from libs.wende import WenDe
    api = WenDe()

    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    if int(params['pay_status']) != 1:
        return HttpResponse('error')

    if api.pay_sign_ios(params) != params['sign']:
        return HttpResponse('error')

    order_id = params['extend']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'wende_ios', params['out_trade_no'],
                        'wende_ios', 'wende_ios')

def h5_wende_callback(request):
    from libs.wende import WenDe
    api = WenDe()

    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    if int(params['pay_status']) != 1:
        return HttpResponse('error')

    if api.pay_sign_h5(params) != params['sign']:
        return HttpResponse('error')

    order_id = params['extend']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'success', int(float(params['price'])) * 100, 'h5_wende', params['out_trade_no'],
                        'h5_wende', 'h5_wende')

def get_h5_wende_sign(request, params):
    from libs.wende import WenDe
    api = WenDe()
    sign = api.get_h5_pay_sign(params)
    return {'sign': sign}

def h5_1377_callback(request):
    from libs.h5_1377 import Api
    api = Api()

    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v

    if not api.check_pay_sign(params):
        return HttpResponse('3')

    order_id = params['ext']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '1', int(float(params['money'])) * 100, 'h5_1377', params['orderid'], 'h5_1377', 'h5_1377')


def h5_37_callback(request):
    try:
        from libs.h5_37 import Api
        api = Api('Ew..76)JuW854(^fS6eucP*QLC6(~WX')

        ip_whitelist = game_config.system_simple.get('pay_callback_whitelist', {}).get('cn37')
        if ip_whitelist:
            if 'HTTP_X_FORWARDED_FOR' in request.META:
                user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
            else:
                user_ip = request.META.get('REMOTE_ADDR', '')
            if user_ip not in ip_whitelist:
                return HttpResponse(json.dumps({"code": -3, "msg": "", "data": ""}))

        params = {}
        for k, v in request.POST.items():
            if k == 'fx_c_game_id':
                continue
            params[k] = v

        if api.sign(params) != params['sign']:
            return HttpResponse('{"code": -5, "msg": "", "data": ""}')

        order_id = params['order_no']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": "", "data": ""}',
                            int(float(params['money'])) * 100, 'h5_37', params['order_id'], 'h5_37', 'h5_37')
    except:
        utils.print_err()
        return HttpResponse('{"code": -2, "msg": "", "data": ""}')

def ios_37_callback(request):
    try:
        from libs.ios_37 import Api
        api = Api()

        ip_whitelist = game_config.system_simple.get('pay_callback_whitelist', {}).get('cn37')
        if ip_whitelist:
            if 'HTTP_X_FORWARDED_FOR' in request.META:
                user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
            else:
                user_ip = request.META.get('REMOTE_ADDR', '')
            if user_ip not in ip_whitelist:
                return HttpResponse(json.dumps({"code": -3, "msg": "", "data": ""}))

        params = {}
        for k, v in request.REQUEST.items():
            if k == 'fx_c_game_id':
                continue
            params[k] = v

        if not api.check_pay(params):
            return HttpResponse('{"code": -4, "msg": "", "data": ""}')

        order_id = params['order_no']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": "", "data": ""}', 'no_check', 'ios_37',
                            params['order_id'], 'ios_37', 'ios_37')
    except:
        utils.print_err()
        return HttpResponse('{"code": -2, "msg": "", "data": ""}')


def wx_37_callback(request):
    try:
        from libs.h5_37 import Api
        api = Api('#a8e234Mq23Xuk22~^!pw)b569x84VD9')

        ip_whitelist = game_config.system_simple.get('pay_callback_whitelist', {}).get('cn37')
        if ip_whitelist:
            if 'HTTP_X_FORWARDED_FOR' in request.META:
                user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
            else:
                user_ip = request.META.get('REMOTE_ADDR', '')
            if user_ip not in ip_whitelist:
                return HttpResponse(json.dumps({"code": -3, "msg": "", "data": ""}))

        params = {}
        for k, v in request.POST.items():
            if k == 'fx_c_game_id':
                continue
            params[k] = v

        if api.sign(params) != params['sign']:
            return HttpResponse('{"code": -5, "msg": "", "data": ""}')

        order_id = params['order_no']
        try:
            pid, zone, uid, ts, where = order_id.split('|')
        except:
            pid, zone, uid, ts = order_id.split('|')
        ## 额外返利赠送的黄金
        ext_coin = int(params.get('coin', 0))

        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": "", "data": ""}', int(float(params['money'])) * 100, 'wx_37', params['order_id'], 'wx_37', 'wx_37', ext_coin=ext_coin)
    except:
        utils.print_err()
        return HttpResponse('{"code": -2, "msg": "", "data": ""}')

def h5_9130_callback(request):
    from libs.h5_9130 import Api
    api = Api()

    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v


    if not api.verify_pay(params):
        return HttpResponse('6')

    order_id = params['ext']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '0', 'no_check', 'h5_9130', params['order_id'], 'h5_9130', 'h5_9130')


def hf_callback(request):
    try:
        from libs.hf import Api
        api = Api()
        params = json.loads(request.raw_post_data)

        if not api.verify_pay(params):
            return HttpResponse('-1')

        order_id = params['privateField']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, params['cpTradeNo'], 'no_check', 'hf', params['cpTradeNo'], 'hf',
                            'hf')
    except:
        utils.print_err()


def caohua_callback(request):
    try:
        from libs.caohua import Api
        api = Api()
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        if not api.verify_pay(params):
            return HttpResponse(' { "code" : 203, "msg":"签名校验失败", "data" :[] } ')

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{ "code" : 200, "msg":"成功", "data" :[] }', params['pay_amt'],
                            'caohua', params['orderno'], 'caohua', 'caohua')
    except:
        utils.print_err()


def h5_ch_callback(request):
    try:
        from libs.caohua import ApiH5
        api = ApiH5()
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        if not api.verify_pay(params):
            return HttpResponse(' { "code" : 203, "msg":"签名校验失败", "data" :[] } ')

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{ "code" : 200, "msg":"成功", "data" :[] }', params['pay_amt'], 'h5_ch',
                            params['orderno'], 'h5_ch', 'h5_ch')
    except:
        utils.print_err()


def h5_ch2_callback(request):
    try:
        from libs.caohua import ApiH5_2
        api = ApiH5_2()
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        if not api.verify_pay(params):
            return HttpResponse(' { "code" : 203, "msg":"签名校验失败", "data" :[] } ')

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{ "code" : 200, "msg":"成功", "data" :[] }', params['pay_amt'], 'h5_ch2',
                            params['orderno'], 'h5_ch2', 'h5_ch2')
    except:
        utils.print_err()

def r2g_xm_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        key = '19ae20d85bf396e2'
        if hashlib.md5(
                params['username'] + params['time'] + params['game'] + params['serverid'] + params['money'] + params[
                    'orderid'] + params['game_coin'] + params['item'] + hashlib.md5(
                    key + params['site']).hexdigest()).hexdigest() != params['sign']:
            return HttpResponse('{"status":"6","message": "错误的sign"}')

        order_id = params['item']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"status":"0","message": ""}', 'no_check', 'r2g_xm',
                            params['orderid'], 'r2g_xm', 'r2g_xm')
    except:
        utils.print_err()
        
def r2g_zone_list(request):
    
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api()
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': []}))
    data = []
    now = datetime.datetime.now()
    for k,v in sorted(game_config.zone.items(), key=lambda x:x[1][2]):
        if v[2] > now:
            continue
        if v[8] == 0:
            data.append({'serverid': k, 'servername': v[0], 'status': 'enabled'})
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': data}))

def r2g_role_info(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api()
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    username = params['username']
    zone = params['serverid']
    rolename = params['rolename']
    pf_key = '%s|r2g_kr' % username
    user_zone = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone:
        return HttpResponse(json.dumps({'status': 2, 'msg': 'fail', 'data': {}}))
    user_zone = user_zone[0]

    data = cache.get('r2g_role_info_%s_%s' % (user_zone.uid, zone), None)
    if not data:
        app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
        data = {
            'username': username,
            'rolename': app_user['uname'],
            'roleid': user_zone.uid,
            'level': app_user['home']['building001']['lv']
            }
        cache.set('r2g_role_info_%s_%s' % (user_zone.uid, zone), data, 300)
    if rolename != data['rolename']:
        return HttpResponse(json.dumps({'status': 2, 'msg': 'fail', 'data': {}}))
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': data}))

def r2g_pay_list(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api()
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    data = [
        'pay1',
        'pay2',
        'pay3',
        'pay4',
        'pay5',
        'pay6',
        'pay7',
        'pay8',
        ]
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': {'productids': data}}))

def r2g_create_roder(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api()
    if not api.check_sign(params, stype='pay'):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    uid = params['roleid']
    zone = params['serverid']
    pid = params['productid']
    user_zone = UserZone.get(uid)
    if not user_zone:
        return HttpResponse(json.dumps({'status': 2, 'msg': 'fail', 'data': {}}))
    order_id = '%s|%s|%s|%s' % (pid, zone, uid, int(time.time()))
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': {'transactionid': order_id}}))


def r2g_kr_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        key = '15ee0672f5cbe002'
        if hashlib.md5(
                params['username'] + params['time'] + params['game'] + params['serverid'] + params['money'] + params[
                    'orderid'] + params['game_coin'] + params['item'] + hashlib.md5(
                    key + params['site']).hexdigest()).hexdigest() != params['sign']:
            return HttpResponse('{"status":"6","message": "错误的sign"}')

        order_id = params['item']
        order_id_list = order_id.split('|')
        if order_id_list[0] == 'pc':
            pay_from = 'r2g_kr_pc'
            pc, pid, zone, r2_uid, ts = order_id.split('|')
            pf_key = '%s|r2g_kr' % r2_uid
            user_zone_list = list(UserZone.query({'pf_key': pf_key}))
            user_zone = user_zone_list[0]
            uid = user_zone.uid
        else:
            pay_from = 'r2g_kr'
            pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"status":"0","message": ""}', 'no_check', 'r2g_kr',
                            params['orderid'], pay_from, 'r2g_kr')
    except:
        utils.print_err()


def r2g_kr_callback_old(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        key = '15ee0672f5cbe002'
        if hashlib.md5(
                params['username'] + params['time'] + params['game'] + params['serverid'] + params['money'] + params[
                    'orderid'] + params['game_coin'] + params['item'] + hashlib.md5(
                    key + params['site']).hexdigest()).hexdigest() != params['sign']:
            return HttpResponse('{"status":"6","message": "错误的sign"}')

        order_id = params['item']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"status":"0","message": ""}', 'no_check', 'r2g_kr',
                            params['orderid'], 'r2g_kr', 'r2g_kr')
    except:
        utils.print_err()


def h5_360_callback(request):
    try:
        qid = request.REQUEST.get('qid')
        server_id = request.REQUEST.get('server_id')
        order_amount = request.REQUEST.get('order_amount')
        pf_order_id = request.REQUEST.get('order_id')
        exts = request.REQUEST.get('exts')
        sign = request.REQUEST.get('sign')

        key = '25vTKsEuFG7emteyqZB9y5BLzGi4kCHB'
        md5_str = qid + order_amount + pf_order_id + server_id + exts + key

        if hashlib.md5(md5_str).hexdigest() != sign:
            return HttpResponse('0')

        exts = exts.replace('_a', '/')
        exts = exts.replace('_b', '+')
        exts = exts.replace('_c', '=')

        json_dict = base64.b64decode(exts)
        exis_dict = json.loads(json_dict)
        order_id = exis_dict['item_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '1', int(float(order_amount)) * 100, 'h5_360', pf_order_id, 'h5_360', 'h5_360')
    except:
        utils.print_err()


def h5_360_2_callback(request):
    try:
        game_secret = 'dbc2f58e1527b7e026c1f8ca8b0c98ab'
        req_params = {}
        for k, v in request.REQUEST.items():
            if not v:
                continue
            req_params[k] = v
        params_sort = sorted(req_params.keys())
        md5_str = ''
        for k in params_sort:
            if k == 'sign':
                continue
            md5_str += '%s%s' % (k, req_params[k])
        md5_str += game_secret
        sign = hashlib.md5(md5_str).hexdigest()
        if sign != req_params['sign']:
            return HttpResponse('sign error')
        order_id = req_params['order_id']
        pid, zone, uid, ts = order_id.split('|')
        pf_order_id = req_params['plat_order_id']
        channel = 'ald_%s' % req_params['channel']

        return add_user_pay(order_id, uid, zone, pid, 'ok', 'no_check', req_params['plat_user_id'], pf_order_id,
                            channel, channel)
    except:
        utils.print_err()


def h5_360_3_callback(request):
    try:
        secret = '93a545f6b58c09b8bc236de2993626bb'
        req_params = {}
        for k, v in request.REQUEST.items():
            if not v:
                continue
            req_params[k] = v
        params_sort = sorted(req_params.keys())
        md5_str = ''
        for k in params_sort:
            if k == 'sign':
                continue
            md5_str += '&%s=%s' % (k, req_params[k])
        md5_str = md5_str[1:]
        sign1 = hashlib.md5(md5_str).hexdigest()
        sign = hashlib.md5(sign1 + secret).hexdigest()
        if sign != req_params['sign']:
            return HttpResponse('sign error')
        order_id = req_params['extradata']
        pid, zone, uid, ts = order_id.split('|')
        pf_order_id = req_params['orderid']
        channel = 'sy_%s' % req_params['sdkindx']

        return add_user_pay(order_id, uid, zone, pid, 'ok', req_params['feemoney'], req_params['uid'], pf_order_id, channel,
                            channel)
    except:
        utils.print_err()


def h5_twyx_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        key = '6c2e482f517f526a5ad143d87ec707ef'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'h5_twyx',
                            params['orderid'], 'h5_twyx', 'h5_twyx')
    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg": "系统错误"}')


def h5_twyx2_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = 'e33d115f4f55b12fac4010437876c34c'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100,
                            'h5_twyx2', params['orderid'], 'h5_twyx2', 'h5_twyx2')
    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg": "系统错误"}')

def pc_twyx2_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = '534377b57f510f0d0a5f1c084ce11580'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100,
                            'h5_twyx2', params['orderid'], 'h5_twyx2', 'h5_twyx2')
    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg": "系统错误"}')

def h5_twyx3_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = '7713c5d6e60589f25755229a60114623'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'h5_twyx3',
                            params['orderid'], 'h5_twyx3', 'h5_twyx3')
    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg": "系统错误"}')


def h5_twyx4_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = '70eac6997722f2689ce85ea5e95d059e'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'h5_twyx4',
                            params['orderid'], 'h5_twyx4', 'h5_twyx4')
    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg": "系统错误"}')

def h5_twyx5_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = '7a4657b07c5fecc0198720199a1ca450'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'h5_twyx5',
                            params['orderid'], 'h5_twyx5', 'h5_twyx5')
    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg": "系统错误"}')

def pc_twyx5_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = '9347691058d2990a2d1fb8bcfc82f0f8'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'h5_twyx5',
                            params['orderid'], 'h5_twyx5', 'h5_twyx5')
    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg": "系统错误"}')


def h5_kuku_callback(request):
    req_params = json.loads(urllib.unquote(request.raw_post_data))
    key = u'3179f2b7-1265-453b-b5bb-6b620f44a3ee'
    sign_str = ''.join(unicode(req_params[k]) for k in sorted(req_params.keys()) if k != 'sign') + key
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    if sign != req_params['sign']:
        return HttpResponse('fail')

    order_id = req_params['gameOrderNo']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'OK', 'no_check', 'h5_kuku', req_params['orderNo'], 'h5_kuku',
                        'h5_kuku')


def get_h5_bg_sign(request, params):
    key = u'1C1CPKQUPHAD05FC'
    sign_str = '&'.join(unicode(k) + '=' + unicode(params[k]) for k in sorted(params.keys()) if k != 'sign') + key
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()

    return {'sign': sign}


def h5_bg_callback(request):
    req_params = {}
    for k, v in request.POST.items():
        req_params[k] = v

    key = u'1C1CPKQUPHAD05FC'
    sign_str = '&'.join(
        unicode(k) + '=' + unicode(urllib.quote_plus(req_params[k])) for k in sorted(req_params.keys()) if
        k != 'sign') + key
    sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()

    if sign != req_params['sign']:
        return HttpResponse('{"status":"fail"}')

    order_id = req_params['trade_no']
    pid, zone, uid, ts = order_id.split(';')

    return add_user_pay(order_id, uid, zone, pid, '{"status":"success"}', 'no_check', 'h5_bg',
                        req_params['out_trade_no'], 'h5_bg', 'h5_bg')


def u37_ios_callback(request):
    from libs.h5_37 import Api
    api = Api('7JfDyjUaiGF0tX4qsMdk5Cxu2IRYg6HA')

    params = {}
    for k, v in request.POST.items():
        params[k] = v

    if api.sign(params) != params['sign']:
        return HttpResponse('{"code": -4, "msg": ""}')

    order_id = params['order_no']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": ""}', 'no_check', '37_ios', params['order_id'],
                        '37_ios', '37_ios')
def get_37_sid(zone):
    ## 合服区ID转换成数字 h1_501 --> 100100501, h2_501 --> 100200501
    if game_config.zone[zone][8]:
        m, n = zone.split('_')
        sid = (1000 + int(m[1:])) * 100000 + int(n)
    else:
        sid = int(zone)
    return sid

def parse_37_sid(sid):
    if int(sid) < 100000:
        return sid
    postfix = int(sid) % 100000
    prefix = int(sid) / 100000 - 1000
    return 'h%s_%s' % (prefix, postfix)

def _get_zone_list(pf):
    zone_list = []
    zone_pf_config = game_config.zone_pf
    if pf in ['h5_37']:
        zone_groups = ['37game']
    elif pf == 'h5_twyx':
        zone_groups = ['tanwan']
    elif pf in ['wx_37', 'fs_wx_37', 'cb_37_cn']:
        zone_groups = game_config.help_msg['zone_groups'].keys()
    else:
        zone_groups = []
    for zone, value in game_config.zone.items():
        if not UserZone.belong_to_zone_groups(zone, zone_groups):
            continue
        if pf in ['h5_37','wx_37', 'cb_37_cn', 'fs_wx_37']:
            zone_id = get_37_sid(zone)
            zone_list.append(
                {"dsid": zone_id, "dsname": value[0], "start_time": value[2].strftime('%Y-%m-%d %H:%M:%S'), "statue": 1, })
        elif pf == 'h5_twyx':
            if value[8]:
                continue
            zone_list.append({
                'server_id': zone,
                'server_name': value[0],
                'server_type': 'normal',
                'open_time': int(time.mktime(value[2].timetuple()))
            })
    return zone_list

def _get_twyx_sign(params, pf):
    if pf == 'h5_twyx2':
        pay_key = 'e33d115f4f55b12fac4010437876c34c'
    elif pf == 'h5_twyx3':
        pay_key = '7713c5d6e60589f25755229a60114623'
    _p_list = ['%s=%s' % (k,v) for k,v in sorted(params.iteritems(), key=lambda x:x[0])]
    _sign_str = '&'.join(_p_list) + pay_key
    return hashlib.md5(_sign_str).hexdigest()

def twyx_zone_list(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            if k in ['sign', 'ext']:
                continue
            params[k] = v
        sign = request.REQUEST['sign']
        ext = request.REQUEST['ext']
        if sign != _get_twyx_sign(params, ext):
            return HttpResponse(json.dumps({
                'code': 100,
                'message': u'签名校验失败',
                'data': []
            }))
        pf_uid = params.get('user_id')
        start_time = params.get('start_time')
        end_time = params.get('end_time')
        if pf_uid:
            zone_list = []
            pf_key = '%s|%s' % (pf_uid, ext)
            user_zone = list(UserZone.query({'pf_key': pf_key}))
            if not user_zone:
                return HttpResponse(json.dumps({
                    'code': 101,
                    'message': u'用户不存在',
                    'data': []
                }))
            user_zone = user_zone[0]
            for k,v in user_zone.zone_login.items():
                zone_list.append({
                    'server_id': k,
                    'server_name': game_config.zone[k][0],
                    'server_type': 'normal',
                    'open_time': int(time.mktime(game_config.zone[k][2].timetuple()))
                })
        else:
            zone_list = _get_zone_list('h5_twyx')
        _zone_list = []
        for item in sorted(zone_list, key=lambda x:x['open_time']):
            if start_time and item['open_time'] < int(start_time):
                continue
            if end_time and item['open_time'] > int(end_time):
                continue
            _zone_list.append(item)
        page_num = int(params.get('page_num', 1))
        page_size = int(params.get('page_size', 500))
        s = (page_num - 1) * page_size
        e = s + page_size
        zone_data = _zone_list[s:e]
        return HttpResponse(json.dumps({
            'code': 0,
            'message': u'成功',
            'data': zone_data,
            'page_num': page_num,
            'page_size': page_size,
            'total': len(zone_list)
        }))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({
            'code': 105,
            'message': 'system error',
            'data': []
        }))

def twyx_get_user_role(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            if k in ['sign', 'ext']:
                continue
            params[k] = v
        sign = request.REQUEST['sign']
        ext = request.REQUEST['ext']
        if sign != _get_twyx_sign(params, ext):
            return HttpResponse(json.dumps({
                'code': 100,
                'message': u'签名校验失败',
                'data': []
            }))
        pf_uid = params['user_id']
        zone = params['server_id']
        pf_key = '%s|%s' % (pf_uid, ext)
        user_zone = list(UserZone.query({'pf_key': pf_key}))
        if not user_zone:
            return HttpResponse(json.dumps({
                'code': 101,
                'message': u'用户不存在',
                'data': []
            }))
        user_zone = user_zone[0]

        data = cache.get('twyx_get_role_%s_%s' % (user_zone.uid, user_zone.zone_login[zone]['merge_zone']), None)
        if not data:
            app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
            data = {
                'user_id': pf_uid,
                'game_role_name': app_user['uname'],
                'game_role_id': user_zone.uid,
                'server_id': zone,
                'game_role_lv': app_user['home']['building001']['lv']
                }
            cache.set('twyx_get_role_%s_%s' % (user_zone.uid, user_zone.zone_login[zone]['merge_zone']), data, 300)

        return HttpResponse(json.dumps({
            'code': 0,
            'message': u'成功',
            'data': [data]
        }))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({
            'code': 105,
            'message': 'system error',
            'data': []
        }))


def twyx_create_order(request):
    try:
        PRODUCT_ID_MAPS = {
            '10006': 'pay1',
            '10030': 'pay2',
            '10068': 'pay3',
            '10128': 'pay4',
            '10328': 'pay5',
            '10648': 'pay6',
            '10998': 'pay7',
            '11998': 'pay8',
        }
        params = {}
        for k,v in request.REQUEST.items():
            if k in ['sign', 'ext']:
                continue
            params[k] = v
        sign = request.REQUEST['sign']
        ext = request.REQUEST['ext']
        if sign != _get_twyx_sign(params, ext):
            return HttpResponse(json.dumps({
                'code': 100,
                'message': u'签名校验失败',
                'data': []
            }))
        uid = params['game_role_id']
        zone = params['server_id']
        pf_uid = params['user_id']
        pf_key = '%s|%s' % (pf_uid, ext)
        user_zone = UserZone.get(uid)
        if not user_zone:
            return HttpResponse(json.dumps({
                'code': 101,
                'message': u'用户不存在',
                'data': []
            }))
        if zone not in user_zone.zone_login:
            return HttpResponse(json.dumps({
                'code': 102,
                'message': u'所选区中无角色',
                'data': []
            }))
        if pf_key != user_zone.pf_key:
            return HttpResponse(json.dumps({
                'code': 103,
                'message': u'平台账号与角色ID不匹配',
                'data': []
            }))
        pid = PRODUCT_ID_MAPS.get(params.get('product_id'))
        if not pid:
            return HttpResponse(json.dumps({
                'code': 104,
                'message': u'充值档位不存在',
                'data': []
            }))
        if game_config.pay_config[pid][0] != int(params['money']):
            return HttpResponse(json.dumps({
                'code': 104,
                'message': u'支付金额与充值档位不匹配',
                'data': []
            }))
        order_id = '%s|%s|%s|%s' % (pid, zone, uid, int(time.time()))
        return HttpResponse(json.dumps({
            'code': 0,
            'message': u'成功',
            'data': {
                'ext': order_id
            }
        }))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({
            'code': 105,
            'message': 'system error',
            'data': []
        }))

def zone_list(request):

    res = {
        "state": 1,
        "data": _get_zone_list('h5_37'),
        "msg": "成功"
    }
    return HttpResponse(json.dumps(res))

def wx_37_zone_list(request):

    res = {
        "state": 1,
        "data": _get_zone_list('wx_37'),
        "msg": "成功"
    }
    return HttpResponse(json.dumps(res))

def jj_zone_list(request):
    res = {
        "state": 1,
        "data": _get_zone_list('jj_h5_37'),
        "msg": "成功"
    }
    return HttpResponse(json.dumps(res))

def cb37_role_list(request):
    user_id = request.REQUEST['user_id']
    server_id = request.REQUEST.get('server_id')
    t = request.REQUEST['time']
    sign = request.REQUEST['sign']
    from libs.h5_37 import Api
    api = Api(pf='cb_37_h5')
    if not server_id:
        _str = '%s%s%s' % (user_id, t, api.sign_key)
    else:
        _str = '%s%s%s%s' % (user_id, server_id, t, api.sign_key)
    _sign = hashlib.md5(_str).hexdigest()
    if sign != sign:
        return HttpResponse(json.dumps({'code': 10003, 'msg': u'签名错误', 'data': None}))
    pf_key = '%s|cb_37_cn' % user_id
    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone_list:
        return HttpResponse(json.dumps({'code': 10001, 'msg': u'用户不存在', 'data': None}))
    user_zone = user_zone_list[0]
    role_list = []
    for k,v in user_zone.zone_login.items():
        zone = get_37_sid(k)
        if server_id and zone != int(server_id):
            continue
        if not game_config.zone[v['merge_zone']][1][0]:
            continue
        data = cache.get('%s_cb37_user_%s' % (user_zone.uid, zone), None)
        if not data:
            app_user = UserZone.call_server_api(k, 'get_user', {'uid': user_zone.uid})
            if not app_user:
                continue
            data = {
                'id': user_zone.uid,
                'name': app_user['uname'],
                'level': app_user['home']['building001']['lv'],
                'create_time': int(time.mktime(app_user['add_time'].timetuple())),
                'server_id': zone,
                'server_name': game_config.zone[k][0],
                'gtask': app_user['total_records']['finish_gtask'],
                'country': app_user['country'],
                'official': app_user['official'],
                'country_task': app_user['task']['country'][2]  if app_user['task']['country'] else 0,
                }
            cache.set('%s_wx37_user_%s' % (user_zone.uid, zone), data, 300)
        role_list.append(data)
    return HttpResponse(json.dumps({'code': 1, 'msg': u'成功', 'data': role_list}))

def get_cb37_pay_list(request):
    try:
        t = request.GET['time']
        sign = request.GET['sign']
        from libs.h5_37 import Api
        api = Api(pf='cb_37_h5')
        _sign = hashlib.md5(t+api.sign_key).hexdigest()
        if sign != _sign:
            return HttpResponse(json.dumps({'state': 0, 'msg': 'sign error', 'data': None}))

        server_raise_msg = game_config.server_raise_msg
        return_msg = game_config.return_msg
        data = {}
        for pay_id, value in sorted(game_config.pay_config.iteritems(), key=lambda x:int(x[0][3:])):
            pay_num = int(pay_id[3:])
            if pay_num not in range(1, 9) and pay_num != 219:
                continue
            pay_desc = server_raise_msg.get(pay_id, 'Gold')
            data[pay_id] = {
                'id': pay_id,
                'name': pay_desc,
                'desc': pay_desc,
                'money': value[0],
                'status': 1
            }
        for goods_id, value in sorted(game_config.goods.iteritems(), key=lambda x:int(x[0][2:])):
            pay_id = value['pay_id']
            data[goods_id] = {
                'id': goods_id,
                'name': return_msg.get(value['info'], value['info']),
                'desc': return_msg.get(value['info'], value['info']),
                'money': game_config.pay_config[pay_id][0],
                'status': 1
            }
        return HttpResponse(json.dumps({'code': 1, 'msg': 'success', 'data': data}))
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 1003, 'msg': 'system error', 'data': {}}))

def cb37_zone_list(request):
    appid = request.REQUEST['appid']
    gid = request.REQUEST['gid']
    t = request.GET['time']
    sign = request.GET['sign']
    from libs.h5_37 import Api
    api = Api(pf='cb_37_h5')
    _sign = hashlib.md5(t+appid+gid+api.sign_key).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'state': 0, 'msg': 'sign error', 'data': None}))
    res = {
        "state": 1,
        "data": _get_zone_list('cb_37_cn'),
        "msg": "成功"
    }
    return HttpResponse(json.dumps(res))

def wx37_get_zone(request):
    zone = request.REQUEST['server_id']
    time = request.REQUEST['time']
    sign = request.REQUEST['sign']
    from libs.h5_37 import Api
    api = Api(pf='wx_37')
    _sstr = '%s%s%s' % (zone, time, api.sign_key)
    _sign = hashlib.md5(_sstr).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'code': 10003, 'msg': u'签名错误', 'data': None}))
    if zone not in game_config.zone:
        return HttpResponse(json.dumps({'code': 10001, 'msg': u'参数异常', 'data': None}))
    zone = parse_37_sid(zone)
    data = {'server_name': game_config.zone[zone][0], 'start_time':
            game_config.zone[zone][2].strftime('%Y-%m-%d %H:%M:%S')}
    return HttpResponse(json.dumps({'code': 1, 'msg': u'成功', 'data': data}))

def wx37_role_list(request):
    user_id = request.REQUEST['user_id']
    server_id = request.REQUEST.get('server_id')
    t = request.REQUEST['time']
    sign = request.REQUEST['sign']
    from libs.h5_37 import Api
    api = Api(pf='wx_37')
    if not server_id:
        _str = '%s%s%s' % (user_id, t, api.sign_key)
    else:
        _str = '%s%s%s%s' % (user_id, server_id, t, api.sign_key)
    _sign = hashlib.md5(_str).hexdigest()
    if sign != sign:
        return HttpResponse(json.dumps({'code': 10003, 'msg': u'签名错误', 'data': None}))
    pf_key = '%s|wx_37' % user_id
    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone_list:
        return HttpResponse(json.dumps({'code': 10001, 'msg': u'用户不存在', 'data': None}))
    user_zone = user_zone_list[0]
    role_list = []
    for k,v in user_zone.zone_login.items():
        zone = get_37_sid(v['merge_zone'])
        if server_id and zone != int(server_id):
            continue
        if not game_config.zone[v['merge_zone']][1][0]:
            continue
        data = cache.get('%s_wx37_user_%s' % (user_zone.uid, zone), None)
        if not data:
            app_user = UserZone.call_server_api(k, 'get_user', {'uid': user_zone.uid})
            if not app_user:
                continue
            data = {
                'id': user_zone.uid,
                'name': app_user['uname'],
                'level': app_user['home']['building001']['lv'],
                'create_time': int(time.mktime(app_user['add_time'].timetuple())),
                'server_id': zone,
                'server_name': game_config.zone[k][0],
                'gtask': app_user['total_records']['finish_gtask'],
                'country': app_user['country'],
                'official': app_user['official'],
                'country_task': app_user['task']['country'][2]  if app_user['task']['country'] else 0,
                }
            cache.set('%s_wx37_user_%s' % (user_zone.uid, zone), data, 300)
        role_list.append(data)
    return HttpResponse(json.dumps({'code': 1, 'msg': u'成功', 'data': role_list}))


def hw_callback(request):
    req_params = {}
    for k, v in request.POST.items():
        if k not in ['sign', 'signType']:
            val = v.encode('utf-8')
            kk = k.encode('utf-8')
            req_params[kk] = val
    sign = request.POST.get('sign')
    api = HuaWei()
    if not api.pay_callback_verify(req_params, sign):
        return HttpResponse('1')

    extReserved = request.POST.get('extReserved')
    pid, zone, uid, pf = extReserved.split('|')
    order_id = request.POST.get('requestId')

    return add_user_pay(order_id, uid, zone, pid, '0', int(float(req_params['amount'])) * 100, 'hw',
                        req_params['orderId'], 'hw', 'hw')

def hw_pay_token_verify(request, params):
    try:
        if not isinstance(params, dict):
            purchaseData = json.loads(params)
        else:
            purchaseData = params
        pid, zone, uid, pf = purchaseData['developerPayload'].split("|")
        api = HuaWei(pf=pf)
        if not api.verify_order_token(purchaseData):
            return {'status': 'error', 'msg': 'verify token error'}
        order_id = purchaseData['orderId']
        add_user_pay(order_id, uid, zone, pid, json.dumps({'status': 'success', 'token': purchaseData['purchaseToken']}), int(purchaseData['price']), pf,
                            purchaseData['payOrderId'], pf, pf)
        return {'status': 'success', 'token': purchaseData['purchaseToken']}
    except:
        utils.print_err()
        return {'status': 'error', 'msg': 'verify wrongful'}

def hw_tw_callback(request):
    req_params = {}
    for k, v in request.POST.items():
        if k not in ['sign', 'signType']:
            val = v.encode('utf-8')
            kk = k.encode('utf-8')
            req_params[kk] = val
    sign = request.POST.get('sign')
    api = HuaWei_tw()
    if not api.pay_callback_verify(req_params, sign):
        return HttpResponse('1')

    extReserved = request.POST.get('extReserved')
    pid, zone, uid, pf = extReserved.split('|')
    order_id = request.POST.get('requestId')

    return add_user_pay(order_id, uid, zone, pid, '0', 'no_check', 'hw_tw',
                        req_params['orderId'], 'hw_tw', 'hw_tw')


def hw_tw_pay_token_verify(request, params):
    try:
        purchaseData = params
        pid, zone, uid, pf = purchaseData['developerPayload'].split("|")
        api = HuaWei_tw(pf=pf)
        if not api.verify_order_token(purchaseData):
            return {'status': 'error', 'msg': 'verify token error'}
        order_id = purchaseData['orderId']
        add_user_pay(order_id, uid, zone, pid, json.dumps({'status': 'success', 'token': purchaseData['purchaseToken']}), 'no_check', pf,
                            purchaseData['payOrderId'], pf, pf, pf_pay_money=int(purchaseData['price']))
        return {'status': 'success', 'token': purchaseData['purchaseToken']}
    except:
        utils.print_err()
        return {'status': 'error', 'msg': 'verify wrongful'}

def xh_callback(request):
    req_params = {}
    req_params['openid'] = request.REQUEST.get('openid', None)
    req_params['server_id'] = request.REQUEST.get('server_id', None)
    req_params['server_name'] = request.REQUEST.get('server_name', u'')  # .encode('utf-8')
    req_params['tm'] = request.REQUEST.get('tm', None)
    req_params['app_id'] = request.REQUEST.get('app_id', None)
    req_params['app_order_no'] = request.REQUEST.get('app_order_no', None)
    req_params['r_order_no'] = request.REQUEST.get('r_order_no', None)
    req_params['role_name'] = request.REQUEST.get('role_name', u'')  # .encode('utf-8')
    req_params['cch_id'] = request.REQUEST.get('cch_id', None)
    req_params['order_status'] = request.REQUEST.get('order_status', None)
    req_params['app_ext'] = request.REQUEST.get('app_ext', None)
    req_params['role_id'] = request.REQUEST.get('role_id', None)
    req_params['amt'] = request.REQUEST.get('amt', None)
    order_id = req_params['app_order_no']

    pid, zone, uid, pf = req_params['app_ext'].split(';')

    if int(req_params['order_status']) != 1:
        return HttpResponse('success')

    from libs.rastar import Rastar
    rastar_api = Rastar()
    sign = rastar_api.get_md5sign(req_params, k_type="pay_key")
    req_params['sign'] = request.REQUEST.get('sign', None)
    # req_params['sign'] = '40f84c3e3e9a36323a6990c0c1bf3a44'

    if req_params['sign'] != sign:
        return HttpResponse('error')

    return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'xh_h5', req_params['r_order_no'], 'xh_xh5',
                        'xh_xh5')


def add_user_pay(order_id, uid, zone, pid, succ_info, pay_amount, pf_other, pf_order_id, pay_from, pf, pf_pay_money=None, ext_coin=0):
    old_zone = zone

    uid = UserZone.get_old_uid(uid,zone)
    user_zone = UserZone.get(uid)
    if user_zone.zone_login.has_key(zone):
        zone = user_zone.zone_login[zone]['merge_zone']

    now = datetime.datetime.now()
    if pf in ['google', 'cb_google', 'cb_google1']:
        onum = select([func.count(pay_records_table.c.id).label('cc')],
                  and_(pay_records_table.c.pf_order_id == pf_order_id)).execute().fetchone()['cc']
    else:
        onum = select([func.count(pay_records_table.c.id).label('cc')],
                      and_(pay_records_table.c.uid == uid, pay_records_table.c.zone == zone,
                           pay_records_table.c.order_id == order_id)).execute().fetchone()['cc']
    if onum > 0:
        if pf in ['tw37', 'ea37']:
            return HttpResponse(json.dumps({'result': 0, 'code': 103, 'msg': 'repeat remark'}))
        else:
            return HttpResponse(succ_info)

    if type(pid) == list:
        pf_pay_money = 0
        pay_money, pf_other = pid
        pid_num_list = pf_other.split('|')
        pid_list = []
        pay_coin = 0
        for item in pid_num_list:
            pid, pnum = item.split('_')
            for i in range(int(pnum)):
                pid_list.append(pid)
                pay_coin += game_config.pay_config[pid][1]
        pid = pid_list
        if game_config.ploy.has_key('member_card'):
            if game_config.ploy['member_card']['pay'] == pay_money or game_config.ploy['member_card']['pay'] == pid:
                user_zone.member = 1
        goods_id = ','.join(pid)
    else:
        goods_config = game_config.goods.get(pid,None)
        if goods_config:
            pay_money = game_config.pay_config[goods_config['pay_id']][0]
            pay_coin = 0
        else:
            pay_config = game_config.pay_config[pid]
            pay_money = pay_config[0]
            pay_coin = pay_config[1]
        goods_id = pid

        AdClick.check_click(user_zone.phone_id, uid=user_zone.uid, event_type=2)
        if game_config.ploy.has_key('member_card'):
            if game_config.ploy['member_card']['pay'] == pay_money or game_config.ploy['member_card']['pay'] == pid:
                user_zone.member = 1
        if pf_pay_money is None:
            if goods_config:
                pf_pay_money = game_config.system_simple.get('pf_pay_money', {}).get(goods_config['pay_id'], 0)
            else:
                pf_pay_money = game_config.system_simple.get('pf_pay_money', {}).get(pid, 0)

    if pay_amount != 'no_check' and int(pay_money * 100) != int(float(pay_amount)):
        raise Exception('pay_money error')

    order_values = {'zone': zone, 'old_zone': old_zone, 'uid': uid, 'order_id': order_id, 'pay_coin': pay_coin,
                    'pay_money': pay_money, 'pf_pay_money': pf_pay_money, 'pay_time': now, 'pf_other': pf_other,
                    # 港澳台东南亚赠送金砖
                    'pf_order_id': pf_order_id, 'pay_from': pay_from,  # 港澳台东南亚赠送money
                    'pf': user_zone.pf, 'status': 0, 'country': -1, 'lv': 1, 'power': 0, 'goods_id': goods_id}
    # return HttpResponse(succ_info)
    last_id = pay_records_table.insert(values=order_values).execute().last_inserted_ids()[0]
    if pf == 'mi' and user_zone.pf != 'mi':
        return HttpResponse(succ_info)


    pay_result = UserZone.call_server_api(old_zone, 'user_pay', {'uid': uid, 'pid': pid, 'pay_id': last_id, 'ext_coin': ext_coin}, retry=5)
    if pay_result['succ'] == 1:
        country = pay_result['country']
        pay_coin = pay_result['pay_coin']
        power = pay_result['power']
        lv = pay_result['lv']

        pay_records_table.update(and_(pay_records_table.c.id == last_id)).execute(status=1, country=country,
                                                                                  pay_coin=pay_coin, lv=lv, power=power)
        user_zone.pay_money += int(pay_money)
        user_zone.save()

    return HttpResponse(succ_info)


def zfb_notify(request):
    from libs.zhifubao import ZhiFuBao
    api = ZhiFuBao(settings.ZFB['partner'], settings.ZFB['key'], settings.ZFB['notify_url'],
                   settings.ZFB['call_back_url'])
    params = {}
    params['service'] = request.REQUEST.get('service')
    params['v'] = request.REQUEST.get('v')
    params['sec_id'] = request.REQUEST.get('sec_id')
    params['sign'] = request.REQUEST.get('sign')
    params['notify_data'] = request.REQUEST.get('notify_data')

    sign = params['sign']

    local_sign = api._private_notify_sig(params)
    if sign.lower() != local_sign.lower():
        return HttpResponse('ERROR')
    notify_data = api.get_notify_data(params['notify_data'].encode('utf-8'))
    if notify_data['trade_status'].upper() != 'TRADE_FINISHED':
        return HttpResponse('ERROR')
    order_id = notify_data['out_trade_no']
    uid, t, pid, zone, pf = order_id.split('|')
    pay_amount = int(float(notify_data['total_fee'])) * 100
    return add_user_pay(order_id, uid, zone, pid, 'success', pay_amount, notify_data['buyer_email'],
                        notify_data['trade_no'], 'zfb', pf)


def zfb_callback(request):
    return_game = request.GET.get('return_game', None)
    if return_game:
        return HttpResponse('Return Game')

    request_items = request.REQUEST.items()
    params = {}
    for item in request_items:
        params[item[0]] = item[1]
    sign = params['sign']
    del params['sign']
    del params['sign_type']
    if params['result'] != 'success':
        return HttpResponse('error')
    from libs.zhifubao import ZhiFuBao
    api = ZhiFuBao(settings.ZFB['partner'], settings.ZFB['key'], settings.ZFB['notify_url'],
                   settings.ZFB['call_back_url'])

    local_sign = api._private_sig(params)
    if local_sign.upper() != sign.upper():
        return HttpResponse('error')
    return render_to_response('zfb_callback.html',
                              {'trade_no': params['trade_no'], 'out_trade_no': params['out_trade_no'], },
                              RequestContext(request))


def yyb_pay(request, params):
    pid, zone, uid, pf = params['ext'].split(';')
    pay_money = get_config_pay_money(pid)

    yyb_api = YybPay(pf=pf)
    openid = params['open_id']
    login_type = params['login_type']
    if login_type == 'qq':
        openkey = params['payToken']
    else:
        openkey = params['accessToken']
    yyb_pf = params['yyb_pf']
    pfkey = params['pf_key']
    order_id = '%s|%s|%s' % (uid, int(time.time()), login_type)
    for i in xrange(5):
        try:
            res = yyb_api.pay(openid, openkey, yyb_pf, pfkey, login_type, order_id, pay_money)
            if res['ret'] == 0:
                pf_other = res['balance']
                if pf == 'yyb2':
                    pf_other = 'yyb2'
                elif pf == 'test_yyb':
                    pf_other = 'test_yyb'
                elif pf == 'yyb_gdt':
                    pf_other = 'yyb_gdt'
                succ_info = add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', pf_other, res['billno'],
                                         'yyb%s' % login_type, pf)
            break
        except:
            time.sleep(0.5)
            continue
    return 'ok'


def ios_pay(request, params):
    pid, zone, uid, pf = params['ext'].split(';')
    pay_config = game_config.pay_config
    receipt = params['receipt']

    print '=========================='
    print pid, zone, uid, pf

    goods_config = game_config.goods.get(pid,None)
    if goods_config:
        pay_id = goods_config['pay_id']
    else:
        pay_id = pid

    from libs.appstore_pay import AppStorePay
    for i in range(5):
        try:
            res = AppStorePay(receipt).pay()
            break
        except:
            utils.print_err()
            continue
    else:
        return 'ok'

    if res is False:
        return 'ok'

    try:
        for item in res['receipt']['in_app']:
            _order_id = item['transaction_id']
            order_id = '%s|%s|%s|%s' % (pid, uid, _order_id, pf)
            _pid = item['product_id']
            print _order_id, _pid
            onum = select([func.count(pay_records_table.c.id).label('cc')],
                          and_(pay_records_table.c.pf_order_id == _order_id)).execute().fetchone()['cc']
            if onum > 0:
                continue
            for k, v in pay_config.items():
                if _pid in v[7] and pay_id == k:
                    succ_info = add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'no', _order_id, 'ios', pf)
                    break

    except:
        utils.print_err()

    print '============================'

    return 'ok'


def weixin_app_pay(request, params):
    pid = params['pid']
    zone = params['zone']
    uid = params['uid']
    pay_pf = params['pf']
    #强制走萌我爱
    pay_pf = 'weixin_app'
    pay_money = get_config_pay_money(pid)
    if 'HTTP_X_FORWARDED_FOR' in request.META:
        user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
    else:
        user_ip = request.META.get('REMOTE_ADDR', '')
    notify_url = '%s/weixin_app_callback/' % settings.BASE_URL
    api = WeiXinPay(notify_url, pay_pf)
    ts = str(int(time.time()))[2:]
    order_id = '%s|%s|%s|%s' % (uid, pid, zone, ts)
    data = api.pay(str(order_id), pay_money, user_ip, pay_pf.encode('utf-8'))

    return data

def aple_wx_create_order(request, params):
    """
    爱评乐玩主体微信支付下单
    :param request:
    :param params:
    :return:
    """
    pid = params['pid']
    zone = params['zone']
    uid = params['uid']
    pay_pf = params['pf']
    pay_money = get_config_pay_money(pid)
    if 'HTTP_X_FORWARDED_FOR' in request.META:
        user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
    else:
        user_ip = request.META.get('REMOTE_ADDR', '')
    notify_url = '%s/weixin_app_callback/' % settings.BASE_URL
    #api = WeiXinPay(notify_url, 'aipinglewan')
    #api = WeiXinPay(notify_url, 'zhangyoulewan')
    api = WeiXinPay(notify_url, 'youwpz')
    ts = str(int(time.time()))[2:]
    order_id = '%s|%s|%s|%s' % (uid, pid, zone, ts)
    data = api.pay(str(order_id), pay_money, user_ip, pay_pf.encode('utf-8'))

    return data


def zfb_pay(request, params):
    pf = params['pf']
    uid = params['uid']
    zone = params['zone']
    pid = params['pid']
    pay_money = get_config_pay_money(pid)
    if pf == 'wx':
        user_zone = UserZone.get(uid)
        openid = user_zone.pf_key
        session_key = user_zone.pf_pwd
        order_id = '%s|%s' % (uid, int(time.time()))
        wx_api = WXAPI()
        res = wx_api.weixin_pay(openid, session_key, pay_money, order_id)
        if res['errcode'] == 0:
            succ_info = add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', res['balance'], res['bill_no'],
                                     'wx', pf)

        return 'ok'

    from libs.zhifubao import ZhiFuBao
    api = ZhiFuBao(settings.ZFB['partner'], settings.ZFB['key'], settings.ZFB['notify_url'],
                   settings.ZFB['call_back_url'])


    order_id = str(uid) + '|' + str(int(time.time())) + '|' + pid + '|' + str(zone) + '|' + pf
    pay_url = api.get_pay_url(order_id, pay_money, 0)
    return pay_url
    return HttpResponseRedirect(pay_url)


def index(request):
    return HttpResponse('Hello')


def ad_click(request):
    try:
        imei = request.GET['imei'].lower()
        callback_url = request.GET['callback_url']

        ad = AdClick.get(imei)
        if not ad:
            ad = AdClick(imei)
            ad.callback_url = callback_url
            ad.status = 0
            ad.pf = 'toutiao'
            ad.save()
    except:
        utils.print_err()

    return HttpResponse("success")


def bdios_click(request, bd_num):
    try:
        imei = request.GET['idfa'].lower()
        callback_url = request.GET['callback_url']

        akey = 'Mjc0MTk0ODE='
        callback_url = callback_url.replace('{{ATYPE}}', 'activate')
        callback_url = callback_url.replace('{{AVALUE}}', '0')
        sign = hashlib.md5(callback_url + akey).hexdigest()
        callback_url = '%s&sign=%s' % (callback_url, sign)

        callback_url = callback_url
        pf = 'bdios_%s' % bd_num
        AdClick.add_click(imei, pf, callback_url, is_ad=0)
    except:
        utils.print_err()

    return HttpResponse("success")


def bdad_click(request, bd_num):
    try:
        imei = request.GET['imei_md5'].lower()
        callback_url = request.GET['callback_url']

        akey = 'Mjc0MTk0ODE='
        callback_url = callback_url.replace('{{ATYPE}}', 'activate')
        callback_url = callback_url.replace('{{AVALUE}}', '0')
        sign = hashlib.md5(callback_url + akey).hexdigest()
        callback_url = '%s&sign=%s' % (callback_url, sign)

        callback_url = callback_url
        pf = 'bdad_%s' % bd_num
        AdClick.add_click(imei, pf, callback_url, is_ad=0)
    except:
        utils.print_err()

    return HttpResponse("success")


def tt_click(request, tt_num):
    try:
        imei = request.GET['imei'].lower()
        callback_url = request.GET['callback_url']
        pf = 'toutiao_%s' % tt_num

        AdClick.add_click(imei, pf, callback_url, is_ad=0)
    except:
        utils.print_err()

    return HttpResponse("success")


def ttios_click(request, tt_num):
    try:
        imei = request.GET['imei'].lower()
        is_ad = 0
        if imei == '00000000-0000-0000-0000-000000000000':
            imei = request.GET.get('ip', None)
            is_ad = 2
        if not imei:
            return HttpResponse("success")

        callback_url = request.GET['callback_url']
        pf = 'ttios_%s' % tt_num
        AdClick.add_click(imei, pf, callback_url, is_ad=is_ad)
    except:
        utils.print_err()

    return HttpResponse("success")


def uc_ana(request, uc_num):
    try:
        imei = request.GET.get('idfa', None)
        is_ad = 0
        if not imei:
            imei = request.GET['imei']
            is_ad = 1
        callback_url = request.GET['callback'] + '&type=1'
        imei = imei.lower()
        pf = 'uc_%s' % uc_num
        AdClick.add_click(imei, pf, callback_url, is_ad=is_ad)

    except:
        utils.print_err()
    return HttpResponse("success")


def ucios_ana(request, uc_num):
    try:
        imei = request.GET.get('idfa', None)
        is_ad = 0
        if not imei:
            imei = request.GET['imei']
            is_ad = 1
        callback_url = request.GET['callback'] + '&type=1'
        imei = imei.lower()
        pf = 'ucios_%s' % uc_num
        AdClick.add_click(imei, pf, callback_url, is_ad=is_ad)
    except:
        utils.print_err()

    return HttpResponse("success")


def get_xh_sign(request, params):
    return False
    from libs.rastar import Rastar
    api = Rastar()
    sign = api.get_md5sign(params)
    return {'sign': sign}


def get_hw_sign(request, params):
    from libs.huawei import HuaWei
    api = HuaWei()
    sign = api.get_sign(params, key_type='pay')

    return {'sign': sign}


def get_hw_tw_sign(request, params):
    api = HuaWei_tw()
    sign = api.get_sign(params, key_type='pay')

    return {'sign': sign}

def get_mz_sign(request, params):
    from libs.meizu import MeiZu
    api = MeiZu()
    sign = api._hmac_sha1_sig(params)

    return {'sign': sign}


def get_uc_sign(request, params):
    from libs.qyou import Api
    api = Api()
    sign = api._hmac_sha1_sig(params)

    return {'sign': sign}


def gg_callback(request, params):
    from libs.google import Api
    api = Api()

    if not api.validate_purchase(params['str'], params['sign']):
        return HttpResponse('FAILURE')

    order_id = params['order']
    pid, zone, uid, ts = order_id.split('|')
    order_data = json.loads(params['str'])

    goods_config = game_config.goods.get(pid,None)
    if goods_config:
        pay_id = goods_config['pay_id']
    else:
        pay_id = pid

    if order_data['productId'] not in game_config.pay_config[pay_id][7]:
        return HttpResponse('FAILURE')

    return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', 'no_check', 'google', order_data['orderId'], 'google',
                        'google')

def gg_callback_v3(request, params):
    """
    最新版回调地址
    """
    from libs.google import Api
    api = Api()

    if not api.validate_purchase_v3(params['packageName'], params['productId'], params['purchaseToken']):
        return HttpResponse('FAILURE')

    order_id = params['order']
    if not order_id:
        print datetime.datetime.now(), ">>>>gg_callback_v3 error<<<<", params
        return HttpResponse('SUCCESS')
    pid, zone, uid, ts = order_id.split('|')
    goods_config = game_config.goods.get(pid,None)
    if goods_config:
        pay_id = goods_config['pay_id']
    else:
        pay_id = pid

    if params['productId'] not in game_config.pay_config[pay_id][7]:
        return HttpResponse('FAILURE')

    return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', 'no_check', 'google', params['orderId'], 'google',
                        'google')

def cb_gg_callback(request, params):
    from libs.google import Api
    pf = params.get('pf', 'cb_google')
    if pf == 'cb_yuanqu_gg':
        api = Api(pf='cb_yuanqu_gg')
    else:
        api = Api()

    if pf == 'cb_yuanqu_gg':
        if not api.validate_purchase_jwt(params['packageName'], params['productId'], params['purchaseToken']):
            return HttpResponse('FAILURE')
    else:
        if not api.validate_purchase_v3(params['packageName'], params['productId'], params['purchaseToken']):
            return HttpResponse('FAILURE')

    order_id = params['order']
    pid, zone, uid, ts = order_id.split('|')

    goods_config = game_config.goods.get(pid,None)
    if goods_config:
        pay_id = goods_config['pay_id']
    else:
        pay_id = pid

    if params['productId'] not in game_config.pay_config[pay_id][7]:
        return HttpResponse('FAILURE')

    return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', 'no_check', pf, params['orderId'], pf, pf)



def h5_hutao_callback(request):
    from libs.hutao import Api
    api = Api()
    params = {}
    for k, v in request.REQUEST.items():
        if not v:
            continue
        params[k] = v

    if not api.check_pay_sign(params):
        return HttpResponse('{"state": 0, "msg": "Sign Error"}')
    order_id = params['cp_order_num']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '{"state": 1, "msg": "Success"}', params['total_fee'], 'h5_hutao',
                        params['pt_order_num'], 'h5_hutao', 'h5_hutao')


def h5_hutao2_callback(request):
    from libs.hutao import Api
    api = Api(pf='h5_hutao2')
    params = {}
    for k, v in request.REQUEST.items():
        if not v:
            continue
        params[k] = v

    if not api.check_pay_sign(params):
        return HttpResponse('{"state": 0, "msg": "Sign Error"}')
    order_id = params['cp_order_num']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '{"state": 1, "msg": "Success"}', 'no_check', 'h5_hutao2',
                        params['pt_order_num'], 'h5_hutao2', 'h5_hutao2')


@forward_sh_callback
def h5_changxiang_callback(request):
    from libs.changxiang import Api
    api = Api()
    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    if params['state'] != 'SUCCESS':
        return HttpResponse('error')

    if not api.check_pay_sign(params):
        return HttpResponse('error')
    order_id = params['out_order_id']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'success', params['cost_amount'], 'h5_changxiang', params['order_id'],
                        'h5_changxiang', 'h5_changxiang')


def changxiang_callback(request):
    from libs.changxiang import Api
    api = Api(tp='app')
    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    if params['state'] != 'SUCCESS':
        return HttpResponse('error')

    if not api.check_pay_sign(params):
        return HttpResponse('error')
    order_id = params['out_order_id']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, 'success', params['cost_amount'], 'changxiang', params['order_id'], 'changxiang',
                        'changxiang')


@forward_sh_callback
def h5_qq_callback(request):
    try:
        req_params = json.loads(request.raw_post_data)
        from libs.h5_qq import Api
        api = Api()
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"code":1,"msg":"error"}')

        order_id = req_params['bill_no']
        pid, zone, uid, ts = order_id.split('A')
        uid = UserZone.get_old_uid(uid,zone)
        user_zone = UserZone.get(uid)
        succ_info = '{"code":0,"msg":""}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 10, req_params['openid'], req_params['openid'],
                            user_zone.pf, user_zone.pf)
    except:
        utils.print_err()
        return HttpResponse('{"code":0,"msg":""}')

def h5_qqdt_buy_goods(request, params):
    from libs.qqgame import QqGame
    uid = params['uid']
    pid = params['pid']
    zone = params['zone']
    pid_uid_zone = '%s@%s@%s' % (pid,uid,zone)
    pay_config = game_config.pay_config
    pay_money = get_config_pay_money(pid)
    qd_num = pay_money * 10
    coin_num = qd_num
    openid = params.get('openid')
    openkey = params.get('openkey')
    pfkey = params.get('pfkey')
    api = QqGame()
    data = api.buy_goods(openid, openkey, pfkey, pid_uid_zone, qd_num, coin_num, zone)
    return data

def h5_qqdt3_buy_goods(request, params):
    from libs.qqgame import QqGame
    uid = params['uid']
    pid = params['pid']
    zone = params['zone']
    pid_uid_zone = '%s@%s@%s' % (pid,uid,zone)
    pay_config = game_config.pay_config
    pay_money = get_config_pay_money(pid)
    qd_num = pay_money * 10
    coin_num = qd_num
    openid = params.get('openid')
    openkey = params.get('openkey')
    pfkey = params.get('pfkey')
    api = QqGame(pf='h5_qqdt3')
    data = api.buy_goods(openid, openkey, pfkey, pid_uid_zone, qd_num, coin_num, zone)
    return data

def cb_h5_qqdt_buy_goods(request, params):
    from libs.qqgame import QqGame
    uid = params['uid']
    pid = params['pid']
    zone = params['zone']
    pay_config = game_config.pay_config
    pay_money = get_config_pay_money(pid)
    qd_num = pay_money * 10
    if pid in game_config.pay_config:
        coin_num = game_config.pay_config[pid][1]
    else:
        coin_num = 0
    openid = params.get('openid')
    openkey = params.get('openkey')
    api = QqGame(pf='cb_h5_qqdt')
    dawanka_info = api.get_dawanka_info(openid, openkey)
    ext_coin = 0
    if dawanka_info:
        if dawanka_info['payReturnCount'] != 0:
            if pid in game_config.pay_config:
                pay_coin = game_config.pay_config[pid][1]
                ext_coin = int(pay_coin*(dawanka_info['discount']*1.0/100))
    pid_uid_zone = '%s@%s@%s@%s' % (pid,uid,zone,ext_coin)
    pfkey = params.get('pfkey')
    data = api.buy_goods(openid, openkey, pfkey, pid_uid_zone, qd_num, coin_num, zone)
    return data

def h5_qqdt_buy_goods_m(request, params):
    from libs.qqgame import QqGame
    uid = params['uid']
    pid = params['pid']
    zone = params['zone']
    pid_uid_zone = '%s@%s@%s' % (pid,uid,zone)
    pay_config = game_config.pay_config
    pay_money = get_config_pay_money(pid)
    qd_num = pay_money * 10
    coin_num = qd_num
    openid = params.get('openid')
    openkey = params.get('openkey')
    pfkey = params.get('pfkey')
    api = QqGame()
    data = api.buy_goods_m(openid, openkey, pfkey, pid_uid_zone, qd_num, coin_num, zone)
    return data

def h5_qqdt3_buy_goods_m(request, params):
    from libs.qqgame import QqGame
    uid = params['uid']
    pid = params['pid']
    zone = params['zone']
    pid_uid_zone = '%s@%s@%s' % (pid,uid,zone)
    pay_config = game_config.pay_config
    pay_money = get_config_pay_money(pid)
    qd_num = pay_money * 10
    coin_num = qd_num
    openid = params.get('openid')
    openkey = params.get('openkey')
    pfkey = params.get('pfkey')
    api = QqGame(pf='h5_qqdt3')
    data = api.buy_goods_m(openid, openkey, pfkey, pid_uid_zone, qd_num, coin_num, zone)
    return data

def h5_qqdt_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.qqgame import QqGame
        sign = req_params.pop('sig')
        api = QqGame()
        if sign != api.get_sig(req_params, 'GET', '/h5_qqdt_callback/', paycallback=True):
            return '{"ret":1,"msg":"签名验证失败"}'
        order_id = req_params['billno']
        pid_uid_zone = req_params['payitem'].split('*')[0]
        pid, uid, zone = pid_uid_zone.split('@')
        qqgame_confirm_table.insert().execute(
                uid=uid,
                zone=zone,
                data=json.dumps(req_params),
                call_at=datetime.datetime.now()
                )
        return add_user_pay(order_id, uid, zone, pid, '{"ret": 0, "msg": "ok"}', 'no_check', req_params['openid'], req_params['token'], 'h5_qqdt', 'h5_qqdt', req_params['amt'])

    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg":"OK"}')

def h5_qqdt2_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.qqgame import QqGame
        sign = req_params.pop('sig')
        api = QqGame()
        if sign != api.get_sig(req_params, 'GET', '/h5_qqdt2_callback/', paycallback=True):
            return '{"ret":1,"msg":"签名验证失败"}'
        order_id = req_params['billno']
        pid_uid_zone = req_params['payitem'].split('*')[0]
        pid, uid, zone = pid_uid_zone.split('@')
        qqgame_confirm_table.insert().execute(
                uid=uid,
                zone=zone,
                data=json.dumps(req_params),
                call_at=datetime.datetime.now()
                )
        return add_user_pay(order_id, uid, zone, pid, '{"ret": 0, "msg": "ok"}', 'no_check', req_params['openid'], req_params['token'], 'h5_qqdt2', 'h5_qqdt2', req_params['amt'])

    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg":"OK"}')

def h5_qqdt3_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.qqgame import QqGame
        sign = req_params.pop('sig')
        api = QqGame(pf='h5_qqdt3')
        if sign != api.get_sig(req_params, 'GET', '/h5_qqdt3_callback/', paycallback=True):
            return '{"ret":1,"msg":"签名验证失败"}'
        order_id = req_params['billno']
        pid_uid_zone = req_params['payitem'].split('*')[0]
        pid, uid, zone = pid_uid_zone.split('@')
        qqgame_confirm_table.insert().execute(
                uid=uid,
                zone=zone,
                data=json.dumps(req_params),
                call_at=datetime.datetime.now()
                )
        return add_user_pay(order_id, uid, zone, pid, '{"ret": 0, "msg": "ok"}', 'no_check', req_params['openid'], req_params['token'], 'h5_qqdt3', 'h5_qqdt3', req_params['amt'])

    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg":"OK"}')

def cb_h5_qqdt_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.qqgame import QqGame
        sign = req_params.pop('sig')
        api = QqGame(pf='cb_h5_qqdt')
        if sign != api.get_sig(req_params, 'GET', '/cb_h5_qqdt_callback/', paycallback=True):
            return HttpResponse('{"ret":1,"msg":"签名验证失败"}')
        order_id = req_params['billno']
        pid_uid_zone = req_params['payitem'].split('*')[0]
        pid, uid, zone, ext_coin = pid_uid_zone.split('@')
        if int(ext_coin) > 0:
            old_uid = UserZone.get_old_uid(uid, zone)
            user_zone = UserZone.get(old_uid)
            openid,_ = user_zone.pf_key.split('|')
            openkey = user_zone.pf_pwd
            api.report_dawanka_pay(openid, openkey)
        qqgame_confirm_table.insert().execute(
                uid=uid,
                zone=zone,
                data=json.dumps(req_params),
                call_at=datetime.datetime.now()
                )
        return add_user_pay(order_id, uid, zone, pid, '{"ret": 0, "msg": "ok"}',
                'no_check', req_params['openid'], req_params['token'],
                'cb_h5_qqdt', 'cb_h5_qqdt', req_params['amt'],
                ext_coin=int(ext_coin))

    except:
        utils.print_err()
        return HttpResponse('{"ret":0,"msg":"OK"}')

def indofun_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.indofun import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('{"Success": false}')
        order_id = req_params['order_id']
        pid, zone, uid, ts, pf = order_id.split('|')
        ext_coin = int(req_params.get('bonus', 0))
        return add_user_pay(order_id, uid, zone, pid, '{"Success": true}', 'no_check', req_params['payment_amount'],
                            req_params['indofun_trx_id'], pf,
                            'indofun', ext_coin=ext_coin)
    except:
        utils.print_err()
        return HttpResponse('{"Success": true}')

def cb_ad_mycard_auth_global(request, params):
    from libs.mycard import Api
    api = Api()
    data = api.auth_global(params)
    if data['ReturnCode'] == '1':
        auth_code = data['AuthCode']
        onum = select([func.count(mycard.c.id).label('cc')],
                  and_(mycard.c.auth_code == auth_code)).execute().fetchone()['cc']
        if onum > 0:
            return {'status': 'success', 'data': data}

        pid, zone, uid, ts = params['FacTradeSeq'].split('-')
        order_values = {
            'uid': uid,
            'zone': zone,
            'auth_code': data['AuthCode'],
            'fac_trade_seq': params['FacTradeSeq'],
            'trade_seq': data['TradeSeq'],
            'amount': params['Amount'],
            'currency': params['Currency'],
            'payment_type': params['PaymentType'],
            'customer_id': params['CustomerId'],
            'status': 0
        }
        mycard.insert(values=order_values).execute()
        return {'status': 'success', 'data': data}
    else:
        return {'status': 'error', 'msg': data['ReturnMsg']}

def cb_ad_mycard_verify(request, params):
    from libs.mycard import Api
    fac_trade_seq = params['facTradeSeq']
    m = mycard.select(and_(mycard.c.fac_trade_seq == fac_trade_seq)).execute().fetchone()
    if not m:
        return {'status': 'error', 'msg': 'Invalid facTradeSeq'}
    auth_code = m.auth_code
    api = Api()
    trade_data = api.verify_trade(auth_code)
    if trade_data['ReturnCode'] != '1':
        return {'status': 'error', 'msg': trade_data['ReturnMsg']}
    if trade_data['PayResult'] != '3':
        return {'status': 'error', 'msg': 'Transaction failed'}
    order_id = fac_trade_seq
    pid, zone, uid, ts = order_id.split('-')
    add_user_pay(order_id, uid, zone, pid, 'ok', 'no_check', 'cb_ad_mycard',
                 trade_data['MyCardTradeNo'], 'cb_ad_mycard',
                 'cb_ad_mycard', pf_pay_money=trade_data['Amount'])
    mycard.update(and_(mycard.c.auth_code == auth_code)).execute(mycard_trade_no=trade_data['MyCardTradeNo'], payment_type=trade_data['PaymentType'], status=1)
    api.payment_confirm(auth_code)
    return {'status': 'success'}

def cb_ad_mycard_repair(request):
    try:
        params = json.loads(request.POST['DATA'])
        from libs.mycard import Api
        for fac_trade_seq in params['FacTradeSeq']:
            m = mycard.select(and_(mycard.c.fac_trade_seq == fac_trade_seq)).execute().fetchone()
            if not m:
                continue
            if m.status == 1:
                continue
            auth_code = m.auth_code
            api = Api()
            trade_data = api.verify_trade(auth_code)
            if trade_data['ReturnCode'] != '1':
                continue
            if trade_data['PayResult'] != '3':
                continue
            pid, zone, uid, ts = fac_trade_seq.split('-')
            add_user_pay(fac_trade_seq, uid, zone, pid, 'ok', 'no_check', 'cb_ad_mycard',
                         trade_data['MyCardTradeNo'], 'cb_ad_mycard',
                         'cb_ad_mycard', pf_pay_money=trade_data['Amount'])
            mycard.update(and_(mycard.c.auth_code == auth_code)).execute(mycard_trade_no=trade_data['MyCardTradeNo'], payment_type=trade_data['PaymentType'], status=1)
            api.payment_confirm(auth_code)
    except:
        utils.print_err()
    return HttpResponse('ok')

def cb_ad_mycard_diff(request):
    mycard_trade_no = request.POST.get('MyCardTradeNo')
    start_time = request.POST.get('StartDateTime')
    end_time = request.POST.get('EndDateTime')
    data = []
    if mycard_trade_no:
        where = and_(mycard.c.mycard_trade_no == mycard_trade_no)
    else:
        where = and_(mycard.c.pay_time >= start_time, mycard.c.pay_time <= end_time)
    for item in mycard.select(where).execute().fetchall():
        if not item.mycard_trade_no:
            continue
        _item = [item.payment_type, item.trade_seq, item.mycard_trade_no, item.fac_trade_seq, item.customer_id,
                 str(item.amount), item.currency, item.pay_time, '', '']
        data.append(','.join(map(str, _item)) + "<BR>")
    return HttpResponse(''.join(data))

def get_h5_qq_prepay_id(request, params):
    from libs.h5_qq import Api
    api = Api()
    pid, zone, uid, ts = params['order_id'].split('A')
    uid = UserZone.get_old_uid(uid,zone)
    pay_money = get_config_pay_money(pid)
    user_zone = UserZone.get(uid)
    open_id = user_zone.pf_key.split('|')[0]
    session_key = user_zone.pf_pwd
    prepay_id = api.get_prepay_id(open_id, session_key, pay_money, params['order_id'], pid)
    return {'prepay_id': prepay_id}


def get_h5_37_sign(request, params):
    from libs.h5_37 import Api
    api = Api('Ew..76)JuW854(^fS6eucP*QLC6(~WX')
    pid, zone, uid, ts = params['order_no'].split('|')
    pay_money = get_config_pay_money(pid)
    params['money'] = pay_money
    sign = api.sign(params)
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))

    return {'sign': sign, 'ip': ip}

def get_wx_37_sign(request, params):
    from libs.h5_37 import Api
    api = Api('#a8e234Mq23Xuk22~^!pw)b569x84VD9')
    try:
        pid, zone, uid, ts, where = params['order_no'].split('|')
    except:
        pid, zone, uid, ts = params['order_no'].split('|')
    pay_money = get_config_pay_money(pid)
    params['money'] = pay_money
    sign = api.sign(params)
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))

    return {'sign': sign, 'ip': ip}

def get_cb_h5_37_sign(request, params):
    from libs.h5_37 import Api
    api = Api('g3Cth4SJkv6(XtY~v)3^3(7cnya9#4K3')
    pid, zone, uid, ts = params['order_no'].split('|')
    pay_money = get_config_pay_money(pid)
    params['money'] = pay_money
    sign = api.sign(params)
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))

    return {'sign': sign, 'ip': ip}

def get_cb_37_cn_sign(request, params):
    from libs.h5_37 import Api
    api = Api(pf='cb_37_ad')
    action = params.pop('action')
    if action == 'pay':
        pid, zone, uid, ts = params['order_no'].split('|')
        pay_money = get_config_pay_money(pid)
        params['money'] = pay_money
    sign = api.sign(params, action=action)
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))

    return {'sign': sign, 'ip': ip}

def get_ios_37_sign(request, params):
    pid, zone, uid, ts = params['order_no'].split('|')
    pay_money = get_config_pay_money(pid)
    params['money'] = pay_money
    from libs.ios_37 import Api
    api = Api()
    sign = api.get_pay_sign(params)
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))
    return {'sign': sign, 'ip': ip}

def get_h5_7k_sign(request, params):
    from libs.h5_7k import Api7k
    pid, zone, uid, ts = params['order_id'].split('|')
    pay_money = get_config_pay_money(pid)
    params['amt'] = pay_money
    api = Api7k()
    code = api.get_save_code(params)
    return {'code': code}


def get_37_ios_sign(request, params):
    return False
    from libs.h5_37 import Api
    api = Api('7JfDyjUaiGF0tX4qsMdk5Cxu2IRYg6HA')
    sign = api.sign(params)
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))

    return {'sign': sign, 'ip': ip}


def get_h5_muzhi_sign(request, params):
    pid, zone, uid, ts = params['cp_order_id'].split('|')
    pay_money = get_config_pay_money(pid)
    if pay_money * 100 != int(params['amount']):
        raise Exception('h5 muzhi pay amount error')
    from libs.h5_muzhi import Api
    api = Api()
    sign = api.get_pay_sign(params)
    return sign


def get_h5_muzhi2_sign(request, params):
    pid, zone, uid, ts = params['cp_order_id'].split('|')
    pay_money = get_config_pay_money(pid)
    if pay_money * 100 != int(params['amount']):
        raise Exception('h5 muzhi2 pay amount error')
    from libs.h5_muzhi import Api
    api = Api(pf='h5_muzhi2')
    sign = api.get_pay_sign(params)
    return sign


def get_awy_bi_url(request, params):
    secret = 'qka8qKcvRcGN1u0bLA8O'
    uid = params['uid']
    user_zone = UserZone.get(uid)
    cmd = 'getCreateRoleInfo'
    openId = user_zone.pf_key.split('|')[0]
    timed = int(time.time())
    gameId = '663'
    serverId = '1'
    playerName = uid
    sign = hashlib.md5("gameId=%sopenId=%splayerName=%ssecret=%sserverId=%stime=%s" % (
        gameId, openId, playerName, secret, serverId, timed)).hexdigest()
    p = {'cmd': cmd, 'openId': openId, 'time': timed, 'gameId': gameId, 'serverId': serverId, 'playerName': playerName,
         'sign': sign, }
    ec_params = urllib.urlencode(p)
    url = 'https://platform.11h5.com/stat/api/?' + ec_params
    return url


def get_fengkuang_bi_url(request, params):
    secret = '0968919159e50628be40e65747331160'
    uid = params['uid']
    user_zone = UserZone.get(uid)
    openId = user_zone.pf_key.split('|')[0]
    timed = int(time.time())
    gameId = 'zqwz'
    serverId = '1'
    playerName = uid
    sign = hashlib.md5("gameId=%sopenId=%splayerName=%ssecret=%sserverId=%stime=%s" % (
        gameId, openId, playerName, secret, serverId, timed)).hexdigest()
    p = {'openId': openId, 'time': timed, 'gameId': gameId, 'serverId': serverId, 'playerName': playerName,
         'sign': sign, }
    # url = 'http://wxstat.hortor.net/gc/game/player/create'
    return p


def get_h5_360_sign(request, params):
    pid, zone, uid, ts = params['item_id'].split('|')
    pay_money = get_config_pay_money(pid)
    if pay_money != int(params['amount']):
        raise Exception('h5 360 pay amount error')
    md5_str = str(params['item_id']) + str(params['amount']) + str(params['qid'])
    sign = hashlib.md5(md5_str).hexdigest()

    exts_dict = {'item_id': params['item_id'], 'amount': str(params['amount']), 'sign': sign, }
    json_str = json.dumps(exts_dict)
    json_str = json_str.replace(' ', '')

    exts = base64.b64encode(json_str)

    exts = exts.replace('/', '_a')
    exts = exts.replace('+', '_b')
    exts = exts.replace('=', '_c')
    return {'exts': exts}


def get_h5_360_2_sign(request, params):
    game_key = '2939ac42bc83447da980e3d26c137452'
    game_secret = 'dbc2f58e1527b7e026c1f8ca8b0c98ab'
    order_id = params['order_id']
    pid, zone, uid, ts = order_id.split('|')
    uid = UserZone.get_old_uid(uid,zone)
    user_zone = UserZone.get(uid)
    plat_user_id = user_zone.pf_key.split('|')[0]
    pay_money = get_config_pay_money(pid)
    product_name = '充值黄金'
    params = {'game_key': game_key, 'product_name': product_name, 'product_id': pid,
              'notify_url': settings.BASE_URL + '/h5_360_2_callback/', 'plat_user_id': plat_user_id, 'server_id': zone,
              'server_name': zone, 'roleid': uid, 'rolename': uid, 'amount': pay_money, 'timestamp': int(time.time()),
              'order_id': order_id, }
    params_sort = sorted(params.keys())
    md5_str = ''
    for k in params_sort:
        md5_str += '%s%s' % (str(k), str(params[k]))
    md5_str += game_secret

    sign = hashlib.md5(md5_str).hexdigest()
    params['sign'] = sign
    pay_data = json.dumps(params)
    url = 'http://aladdinapi.u.360.cn/user/pay/gettid?pay_data=%s' % pay_data
    res = json.loads(urllib2.urlopen(url).read())

    return {'tid': res['data']['tid']}


def get_h5_360_3_sign(request, params):
    secret = '93a545f6b58c09b8bc236de2993626bb'
    fee = params['fee']
    feeid = params['feeid']
    pay_money = get_config_pay_money(feeid)
    if int(fee) != pay_money * 100:
        raise Exception('h5 360 3 pay amount error')

    md5_str = str(fee) + str(feeid) + secret
    check = hashlib.md5(md5_str).hexdigest()
    return {'check': check}


def get_vivo_sign(request, params):
    from libs.vivo import Vivo
    api = Vivo()
    pid = params['pid']
    uid = params['uid']
    zone = params['zone']
    v = params.get('v')
    pay_money = get_config_pay_money(pid)
    ts = int(time.time())
    order_id = '%s|%s|%s|%s' % (pid, zone, uid, ts)
    notify_url = '%s/vivo_callback/' % settings.BASE_URL
    pay_dict = api.get_pay_dict(order_id, pay_money, notify_url, v)

    return pay_dict


def get_oppo_sign(request, params):
    from libs.oppo import Oppo
    api = Oppo()
    pid = params['pid']
    uid = params['uid']
    zone = params['zone']
    pay_money = get_config_pay_money(pid)
    ts = int(time.time())
    order_id = '%s|%s|%s|%s' % (pid, zone, uid, ts)
    notify_url = '%s/oppo_callback/' % settings.BASE_URL
    pay_dict = api.get_pay_dict(order_id, pay_money, notify_url)

    return pay_dict

@jsonrpc_method('report_ip')
def report_ip(request, params):
    params = pickle.loads(params)
    uid = params['uid']
    ip = params['ip']
    zone = params['zone']
    sessionid = params['sessionid']
    user_zone = UserZone.get(uid)
    if not user_zone:
        return
    ipr = IpaddressRecord.get(ip)
    pf_key = user_zone.pf_key.split('|')[0]
    IpaddressRecord.add_record(uid, zone, ip, pf_key, sessionid)
    return

@jsonrpc_method('if_bind_tel')
def if_bind_tel(request, uid):
    user_zone = UserZone.get(uid)
    if user_zone and user_zone.tel:
        return user_zone.tel
    return False


@jsonrpc_method('get_max_lv_pay')
def get_max_lv_pay(request, uid):
    user_zone = UserZone.get(uid)
    # pay_money = select([func.sum(ASP.c.pay_money).label('cc')],and_(ASP.c.uid==uid)).execute().fetchone()['cc']
    return [user_zone.max_lv, user_zone.pay_money]


@jsonrpc_method('get_member_status')
def get_member_status(request, uid):
    user_zone = UserZone.get(uid)
    return user_zone.member

@jsonrpc_method('get_duplicate_open_date')
def get_duplicate_open_date(request, now):
    return UserZone.get_duplicate_open_date(now=now)

@jsonrpc_method('get_duplicate_dnum')
def get_duplicate_dnum(request, udid):
    user_duplicate = UserDuplicate.get(udid)
    return user_duplicate.dnum

@jsonrpc_method('my_duplicate_zone')
def my_duplicate_zone(request, udid):
    user_duplicate = UserDuplicate.get(udid)
    if user_duplicate:
        uid = int(user_duplicate.uid)
        if uid > settings.UIDADD:
            zone = uid/settings.UIDADD
        else:
            zone = user_duplicate.zone
    else:
        zone = None
    return zone

@jsonrpc_method('join_duplicate')
def join_duplicate(request, join_data):
    try:
        join_data = pickle.loads(join_data)
        uid = str(join_data['uid'])
        open_date = join_data['open_date']
        udid = '%s|%s' % (int(uid)%settings.UIDADD, open_date)
        user_duplicate = UserDuplicate.get(udid)
        if not user_duplicate:
            user_duplicate = UserDuplicate(udid=udid)
        else:
            if user_duplicate.uid != uid:
                return False
        user_duplicate.uid = uid
        user_duplicate.open_date = open_date
        user_duplicate.group_id = join_data['group_id']
        user_duplicate.uname = join_data['uname']
        user_duplicate.pf = join_data['pf']
        user_duplicate.head = join_data['head']
        user_duplicate.zone = join_data['zone']
        user_duplicate.level = join_data['level']
        user_duplicate.prestige = join_data['prestige']
        user_duplicate.join_time= datetime.datetime.now()
        user_duplicate.hero_data = join_data['hero_data']
        user_duplicate.troop_data = join_data['troop_data']
        user_duplicate.pay_money = join_data['pay_money']
        user_duplicate.admin_pay = join_data['admin_pay']
        user_duplicate.power = join_data['power']
        user_duplicate.save()
        return True
    except Exception,e:
        utils.print_err()
        return False

@jsonrpc_method('quit_duplicate')
def quit_duplicate(request, udid):
    user_duplicate = UserDuplicate.get(udid)
    if user_duplicate:
        user_duplicate.delete()
    return True


@jsonrpc_method('validate_reward_code')
def validate_reward_code(request, code):
    reward_cache.judge()
    rid, code_str, n = code.split('-')
    reward = reward_config.rewards['code_rewards'].get(int(rid))
    if not reward:
        return False
    if reward['obj_content']['repeat_use']:
        code_num = reward['obj_content']['code_num']
    else:
        code_num = 1
    reward_code = RewardCode.get(code)
    if not reward_code:
        reward_code = RewardCode(code)
        reward_code.status = 1
        reward_code.uid = 1
        reward_code.rid = rid
        reward_code.save()
    else:
        if reward_code.uid >= code_num:
            return False
        reward_code.uid += 1
        reward_code.save()
    return True

@jsonrpc_method('freeze_from_msg')
def freeze_from_msg(request, uid_zone):
    uid_params = uid_zone.split('|')
    if len(uid_params) == 3:
        uid, zone, status = uid_params
        hour = -1
    else:
        uid, zone, status, freeze_days = uid_params
        hour = int(freeze_days) * 24
    if game_config.zone[zone][8]:
        old_uid = int(uid)%settings.UIDADD
        old_zone = int(uid)/settings.UIDADD
    else:
        old_uid = uid
        old_zone = zone

    status = int(status)
    user_zone = UserZone.get(int(old_uid))
    server_raise_pf = game_config.system_simple['server_raise_pf'].get(user_zone.pf, None)
    if server_raise_pf:
        server_raise_msg = getattr(game_config, 'server_raise_msg_%s' % server_raise_pf)
    else:
        server_raise_msg = game_config.server_raise_msg
    if status == 1:
        freeze_msg = server_raise_msg['freeze_user']
    elif status == 2:
        freeze_msg = server_raise_msg['freeze_chat']
    FreezeLog.add_freeze_log(old_uid, old_zone, 'freeze_msg', status, -1, 'admin', freeze_msg)
    freeze_dict = {'uid': int(old_uid), 'admin_user': 'admin', 'freeze_status': status, 'hour': hour, 'msg': freeze_msg}
    for zone in user_zone.zone_login.keys():
        try:
            UserZone.call_server_api(zone, 'freeze_do', freeze_dict)
        except:
            utils.print_err()
    return True

@jsonrpc_method('change_country_notice')
def change_country_notice(request, params):
    """
    国家公告修改记录
    :param request:
    :param params:
                zone:
                country_id:
                update_time:
                content:
    :return:
    """
    params = pickle.loads(params)
    zone = params['zone']
    cid = params['cid']
    update_time = params['update_time']
    content = params['content']
    zone_cid = '%s|%s' % (zone, cid)
    country_notice = CountryNotice.get(zone_cid)
    if not country_notice:
        country_notice = CountryNotice(zone_cid)

    country_notice.update_time = update_time
    country_notice.content = content
    country_notice.save()
    return True

@jsonrpc_method('get_configs')
def get_configs(request, params):
    try:
        config_cache.judge()
        params = pickle.loads(params)
        use_merge = params.get('use_merge')
        zone = params.get('zone')
        config_data = {'version':  game_config.version}
        for item in reduce(lambda x, y: x + y, game_config.all_config_list, []):
            key = item[0]
            if key in ['pf_system_simple', 'client_config']:
                continue
            config_data[key] = UserZone.get_config_value(key, zone, use_merge=use_merge)
        # config_data = dict([(key, getattr(game_config, key)) for key in
        #                     [item[0] for item in reduce(lambda x, y: x + y, game_config.all_config_list, [])] if
        #                     key not in ['pf_system_simple', 'client_config']])
        # config_data['version'] = game_config.version
        return config_data
    except:
        utils.print_err()

def get_configs_v1(request):
    try:
        config_cache.judge()
        zone = request.GET['zone']
        use_merge = request.GET['use_merge']
        config_data = {'version':  game_config.version}
        for item in reduce(lambda x, y: x + y, game_config.all_config_list, []):
            key = item[0]
            if key in ['pf_system_simple', 'client_config']:
                continue
            config_data[key] = UserZone.get_config_value(key, zone, use_merge=use_merge)
        # config_data = dict([(key, getattr(game_config, key)) for key in
        #                     [item[0] for item in reduce(lambda x, y: x + y, game_config.all_config_list, [])] if
        #                     key not in ['pf_system_simple', 'client_config']])
        # config_data['version'] = game_config.version
        return HttpResponse(json.dumps(config_data, default=Serializer.json_default))
    except:
        utils.print_err()


@jsonrpc_method('get_zone_config')
def get_zone_config(request):
    try:
        config_cache.judge()
        return game_config.zone
    except:
        utils.print_err()

@jsonrpc_method('get_init_server_config')
def get_init_server_config(request):
    try:
        config_cache.judge()
        return {
            'system_simple': game_config.system_simple,
            'zone': game_config.zone
        }
    except:
        utils.print_err()


@jsonrpc_method('get_zone_config_and_version')
def get_zone_config_and_version(request):
    try:
        config_cache.judge()
        return [game_config.zone, game_config.version]
    except:
        utils.print_err()

@jsonrpc_method('get_duplicate_server_config')
def get_duplicate_server_config(request):
    try:
        config_cache.judge()
        return game_config.system_simple['server_port']
    except:
        utils.print_err()


@jsonrpc_method('get_reward_config')
def get_reward_config(request):
    return {'rewards': reward_config.rewards, 'version': reward_config.version}

@jsonrpc_method('get_qqgame_blue')
def get_qqgame_blue(request, uid):
    user_zone = UserZone.get(uid)
    openid,_ = user_zone.pf_key.split('|')
    openkey = user_zone.pf_pwd
    from libs.qqgame import QqGame
    api = QqGame(pf=user_zone.pf)
    user = api.blue_vip_info(openid, openkey)
    return user

@jsonrpc_method('get_dawanka_info')
def get_dawanka_info(request, uid):
    user_zone = UserZone.get(uid)
    openid,_ = user_zone.pf_key.split('|')
    openkey = user_zone.pf_pwd
    from libs.qqgame import QqGame
    api = QqGame(pf=user_zone.pf)
    data = api.get_dawanka_info(openid, openkey)
    return data


@jsonrpc_method('get_server_manage_config')
def get_server_manage_config(request):
    try:
        config_cache.judge()
        auto_open_config = {}
        for item in UserZone.get_zone_group_list():
            zone_group_config = CacheTable.get('auto_open_config_%s' % item['id'], None)
            auto_open_config[item['id']] = zone_group_config
        full_maintain_status = CacheTable.get('full_maintain_status', 1)
        zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
        maintain_config = CacheTable.get('zone_maintain_config', '{}')
        maintain_config = eval(str(maintain_config))
        zone_list = maintain_config.get('zone_list', [])
        return {
            'zone_config': game_config.zone,
            'config_version': game_config.version,
            'auto_open_config': auto_open_config,
            'alert_tel': game_config.help_msg.get('alert_tel', ['18101060123']),
            'maintain': {
                'full_maintain': full_maintain_status,
                'zone_list': zone_list if zone_maintain_status == 0 else []
                }
        }
    except:
        utils.print_err()


@jsonrpc_method('active_zone')
def active_zone(request, params):
    try:
        params = pickle.loads(params)
        zone_id = params['zone_id']
        open_time = params['open_time']
        take_config_type = CacheTable.get('take_config_type', 'setting')
        if take_config_type == 'setting':
            ConfigModel = Configs
        else:
            ConfigModel = TestConfigs
        zone = Zones.get_zone(zone_id)
        if not zone:
            return {
                'status': 'error',
                'msg': u'区服ID（%s）错误' % zone_id
            }
        if zone.active:
            return {
                'status': 'error',
                'msg': u'区服（%s）已经激活' % zone_id
            }
        zone.active = 1
        zone.open_time = open_time
        zone.save()
        disp_config = ConfigModel.get('disp')
        config_value = eval(disp_config.config_value)
        config_value['configs_md5']['zone'] = Zones.get_current_md5()
        disp_config.config_value = str(config_value)
        disp_config.save()
        config_cache.update()
        config_cache.judge()
        return {
            'status': 'success'
        }
    except:
        utils.print_err()

def get_open_time():
    current_data = datetime.date.today()
    open_time = datetime.datetime(current_data.year, current_data.month, current_data.day, 8, 0)
    return open_time

def get_merge_zone_times(zone):
    return int(zone.split('_')[0][1])


@jsonrpc_method('update_merge')
def update_merge(request, params):
    params = pickle.loads(params)
    merge_id = params['merge_id']
    zones = params['zones']
    try:
        Lock.acquire('update_merge')
        r = MergeRecord.get(merge_id)
        if not r:
            Lock.released('update_merge')
            return {
                'status': 'error',
                'msg': 'merge record not existed: %s' % params
            }
        finish_zones = r.finish_zones
        finish_zones.extend(zones)
        r.finish_zones = list(set(finish_zones))
        r.save()
        finish_zone_count = len(r.finish_zones)
        merge_zone_count = len(r.merge_config)
        add_merge_zone = set()
        for z, value in r.merge_config.items():
            for item in value:
                add_merge_zone.add(item['to_zone'])
        if finish_zone_count == merge_zone_count:
            r.status = MergeRecord.merge_finish
            r.progress = 100
            r.end_time = datetime.datetime.now()
            r.save()
            try:
                for add_zone in add_merge_zone:
                    if Zones.get_zone(add_zone):
                        continue
                    z = Zones()
                    z.zone_id = add_zone
                    z.zone_name = add_zone
                    z.host = ''
                    z.port = UserZone.get_service_port(add_zone)
                    z.open_time = get_open_time()
                    z.register_limit = 0
                    z.state = 0
                    z.recommend = 0
                    z.merge_times = get_merge_zone_times(add_zone)
                    z.active = 0
                    z.zone_group = UserZone.get_zone_group(add_zone)
                    z.save()
                for z in r.merge_config.keys():
                    _zone = Zones.get_zone(z)
                    _zone.host = ''
                    _zone.port = 0
                    _zone.active = 2
                    _zone.save()
                take_config_type = CacheTable.get('take_config_type', 'setting')
                if take_config_type == 'setting':
                    ConfigModel = Configs
                else:
                    ConfigModel = TestConfigs
                zone_config = ConfigModel.get('zone')
                zone_config.sub_time = datetime.datetime.now()
                zone_config.save()
            except:
                utils.print_err()
        else:
            r.progress = int(math.ceil((finish_zone_count/(merge_zone_count*1.0))*100))
            r.save()
    except:
        utils.print_err()
    finally:
        Lock.acquire('update_merge')

    _r = MergeRecord.get(merge_id)
    for z in zones:
        if z not in _r.finish_zones:
            return {'status': 'error', 'msg': 'not save'}
    return {'status': 'success'}

@jsonrpc_method('report_country_year_data')
def report_country_year_data(request, params):
    params = pickle.loads(params)
    zone = params['zone']
    years = params['years']
    country = params['country']
    params['report_at'] = datetime.datetime.now()
    db = get_dbengine(zone)
    db_index = db['country_year_data'].index_information()
    if 'years_1' not in db_index:
        db['country_year_data'].create_index('years')
        db['country_year_data'].create_index('zone')
        db['country_year_data'].create_index('country')
        db['country_year_data'].create_index('report_at')
    db['country_year_data'].update({'zone': zone, 'years': years, 'country': country}, {'$set': params}, upsert=True)
    return {'status': 'success'}

@jsonrpc_method('insert_fight_log')
def insert_fight_log(request, params):
    config_cache.judge()
    now = datetime.datetime.now()
    params = pickle.loads(params)
    zone = params['zone']
    if not zone or zone not in game_config.zone:
        return {'status': 'error', 'msg': 'zone error'}
    initJS = params['initJS']
    troop1, troop2 = initJS['troop']
    troop1['uname'] = troop1['uname'].encode('utf-8')
    troop2['uname'] = troop2['uname'].encode('utf-8')
    attack_uid = troop1['uid']
    defense_uid = troop2['uid']
    _data = {
        'initJS': pickle.dumps(initJS),
        'fight_id': params['fight_id'],
        'battle_id': initJS['battle_id'],
        'cid': initJS['cid'],
        'attack_uid': attack_uid,
        'defense_uid': defense_uid,
        'zone': zone,
        'fight_time': params['time'],
        'fight_result': params['fight_result'],
        'collect_uids': [],  # 收藏该战报的玩家uid列表
    }
    db = get_dbengine(zone)
    db['fight_logs'].insert(_data)

    ## 删除过期战报
    clear_time = now - datetime.timedelta(minutes=game_config.system_simple['war_report']['clear_time'][1]) - datetime.timedelta(days=1)
    db['fight_logs'].remove({"zone": zone, "collect_uids.0": {"$exists": False}, "fight_time": {"$lt": clear_time}})
    return {'status': 'success'}

@jsonrpc_method('get_fight_log')
def get_fight_log(request, params):
    config_cache.judge()
    params = pickle.loads(params)
    battle_id = params['battle_id']
    zone = params['zone']
    db = get_dbengine(zone)
    db_index = db['fight_logs'].index_information()
    if 'battle_id_1' not in db_index:
        db['fight_logs'].ensure_index([('battle_id', 1)], unique=True)
        db['fight_logs'].ensure_index([('fight_id', 1)])
        db['fight_logs'].ensure_index([('zone', 1)])
        db['fight_logs'].ensure_index([('attack_uid', 1)])
        db['fight_logs'].ensure_index([('defense_uid', 1)])
        db['fight_logs'].ensure_index([('cid', 1)])
    data = db['fight_logs'].find_one({"battle_id": battle_id})
    if data:
        data['initJS'] = pickle.loads(str(data['initJS']))
    return {'status': 'success', "data": data}


@jsonrpc_method('update_fight_collect')
def update_fight_collect(request, params):
    config_cache.judge()
    params = pickle.loads(params)
    battle_id = params['battle_id']
    action = params['action']
    uid = params['uid']
    zone = params['zone']
    db = get_dbengine(zone)
    data = db['fight_logs'].find_one({"battle_id": battle_id})
    is_update = False
    if data:
        if action == 'collect':
            if uid not in data['collect_uids']:
                data['collect_uids'].append(uid)
                is_update = True
        elif action == 'cancel_collect':
            if uid in data['collect_uids']:
                data['collect_uids'].remove(uid)
                is_update = True
    if is_update:
        db['fight_logs'].update({"battle_id": battle_id}, {"$set": data})
    return {'status': 'success'}


@jsonrpc_method('get_user_data')
def get_user_data(request, params):
    params = pickle.loads(params)
    zone = params['zone']
    uid = params['uid']
    db_conn = ShardMetas['1']['db_engine'].connect()
    user_table = utils.get_table_name('user', zone, settings.USER_TABLE_NUM)
    db_user = db_conn.execute("select * from %s where uid='%s' and zone='%s'" % (user_table, uid, zone)).fetchone()
    if db_user:
        user_data = zlib.decompress(db_user.user_data)
        user_data = eval(user_data)
        user_data['uid'] = int(user_data['uid'])
    else:
        user_data = {}
    db_conn.close()
    return user_data

@jsonrpc_method('get_invitation_data')
def get_invitation_data(request, params):
    params = pickle.loads(params)
    uid = params['uid']
    zone = params['zone']
    if game_config.zone[zone][8] == 0:
        uid = UserZone.get_new_uid(uid, zone)
    r = InvitationRecord.get(uid)
    if not r:
        return {}
    return r.data


@jsonrpc_method('report_invitation_data')
def report_invitation_data(request, params):
    params = pickle.loads(params)
    master_uid = params['master_uid']  # 长uid
    data = params['data']
    old_uid = int(master_uid)%settings.UIDADD
    old_zone = str(int(master_uid)/settings.UIDADD)
    work_uid = data['uid']
    work_zone = data['zone']
    user_zone = UserZone.get(work_uid)
    if not user_zone:
        return {'status': 'success'}
    if user_zone.pf == 'h5_37_wx1':
        return {'status': 'success'}
    if not user_zone.inviter:
        return {'status': 'success'}
    if user_zone.inviter != '2147483647' and user_zone.inviter != str(master_uid):
        return {'status': 'success'}
    if work_zone not in user_zone.zone_login:
        return {'status': 'success'}
    try:
        UserZone.call_server_api(old_zone, 'update_invitation_data', {'uid': old_uid, 'data': data})
    except:
        print "report_invitation_data>>>>>", master_uid, old_uid, old_zone
        utils.print_err()
    return {'status': 'success'}

@jsonrpc_method('gather_player_insert')
def gather_player_insert(request, params):
    params = pickle.loads(params)
    player_id = params['player_id']
    zone = params['zone']
    gather_player = GatherPlayer.get(player_id)
    if not gather_player:
        gather_player= GatherPlayer()
        gather_player.uid = player_id
        gather_player.zone = zone
        gather_player.save()
    return gather_player.status

@jsonrpc_method('gather_player_report')
def gather_player_report(request, params):
    params = pickle.loads(params)
    player_id = params['player_id']
    did = params['did']
    time = params['time']
    data = params['data']
    gather_player = GatherPlayer.get(player_id)
    if not gather_player:
        return False

    data['country'] = None
    power_hero_list = []
    for hid in data['hero'].keys():
        if data['hero'][hid].get('awaken', 0) == 1:
            del data['hero'][hid]['awaken']
        data['hero'][hid]['title'] = None
        data['hero'][hid]['work'] = None
        power_hero_list.append(GatherPlayer.get_fight_prepare_data(hid, data))
    
    url = settings.BATTLE_URL + "/getHeroesData/"
    http_client = HTTPClient()
    resp = http_client.fetch(url, method="POST", body=json.dumps(power_hero_list, default=Serializer.json_default))
    power_list = []
    for item in json.loads(resp.body):
        data['hero'][item['hid']]['power'] = item['power']
        power_list.append(item['power'])
        if data['best_hero_power'] < item['power']:
            data['best_hero_power'] = item['power']
 
    building_lv = data['home']['building001']['lv']
    for item in game_config.system_simple['power_herocount']:
        hero_count = item[1]
        if building_lv <= item[0]:
            break
    power = sum(sorted(power_list,reverse=True)[:hero_count])     
    data['power'] = power
    gather_player_data = GatherPlayerData()
    gather_player_data.did = did
    gather_player_data.uid = player_id
    gather_player_data.time = time
    gather_player_data.data = data
    gather_player_data.save()

    gather_player.times += 1
    open_day, _ = did.split('_')
    if gather_player.days != open_day:
        gather_player.days = int(open_day)
    gather_player.last_time = time
    pay_money, admin_pay, _ = GatherPlayer.get_user_pay(data)
    score = 0
    for k,v in game_config.player_robot['mould_score'].items():
        if k == 'mould_day':
            value = gather_player.days
        elif k == 'mould_times':
            value = gather_player.times
        elif k == 'all_power_range':
            value = data['power']
        elif k == 'one_power_range':
            value = data['best_hero_power']
        elif k == 'building001':
            value = data['home']['building001']['lv']
        elif k == 'office':
            value = data['office']
        elif k == 'pay_true':
            value = pay_money
        elif k == 'pay_false':
            value = admin_pay
        elif k == 'kill_num':
            value = data['total_records']['kill_num']
        elif k == 'build_count':
            value = data['total_records']['build_count']
        if value < v[0] or value > v[1]:
            if value < v[0]:
                a = abs(value - v[0])
            else:
                a = abs(value - v[1])
            one_score = max([v[2]-a*v[3], v[4]])
        else:
            one_score = v[2]
        score += one_score
    for k,v in game_config.player_robot['mould_check_day_score'].items():
        if k in gather_player.day_score:
            continue
        if int(open_day) <= k:
            continue
        day_score = 0
        for _k,_v in v.items():
            if _k == 'all_power_range':
                _value = data['power']
            elif _k == 'one_power_range':
                _value = data['best_hero_power']
            elif _k == 'building001':
                _value = data['home']['building001']['lv']
            elif _k == 'office':
                _value = data['office']
            elif _k == 'pay_true':
                _value = pay_money
            elif _k == 'pay_false':
                _value = admin_pay
            elif _k == 'kill_num':
                _value = data['total_records']['kill_num']
            elif _k == 'build_count':
                _value = data['total_records']['build_count']
            if _value < _v[0] or _value > _v[1]:
                if _value < _v[0]:
                    _a = abs(_value - _v[0])
                else:
                    _a = abs(_value - _v[1])
                _one_score = max([_v[2]-_a*_v[3], _v[4]])
            else:
                _one_score = _v[2]
            day_score += _one_score
        gather_player.day_score[k] = day_score

    for d,s in gather_player.day_score.items():
        if int(open_day) > d:
            score += s
    gather_player.score = score
    gather_player.last_did = gather_player_data.id
    if not gather_player.add_time:
        gather_player.add_time = data['add_time']
    gather_player.login_time = data['online_log']['login_time']
    gather_player.save()
    
    return True

@jsonrpc_method('gather_player_modify')
def gather_player_modify(request, params):
    params = pickle.loads(params)
    player_id = params['player_id']
    status = params['status']
    gather_player = GatherPlayer.get(player_id)
    if not gather_player:
        return False
    if int(status) == 2:
        if gather_player.registrable:
            return False
        else:
            gather_player.status = status
            gather_player.discard_time = datetime.datetime.now()
    else:
        gather_player.status = status
    gather_player.save()
    return True

@jsonrpc_method('get_player_robot')
def get_player_robot(request, params):
    params = pickle.loads(params)
    zone = params['zone']
    count = params['count']
    data = UserZone.get_random_player(zone, count)
    return data

@jsonrpc_method('get_player_data')
def get_player_data(request, params):
    params = pickle.loads(params)
    uid = params['uid']
    c_did = params['c_did']
    zone = params['zone']
    open_day = params['open_day']
    now_d = params['now_d']
    delay_days = params.get('delay_days')
    delay_minutes = params.get('delay_minutes')
    user_zone = UserZone.get(uid)
    gather_player = GatherPlayer.get(user_zone.player_id)
    
    zone_open_time = game_config.zone[zone][2]
    if open_day == 1:
        start_time = zone_open_time
    else:
        start_time = datetime.datetime(*list(now_d.date().timetuple())[:6]) + datetime.timedelta(minutes=game_config.system_simple['deviation'])
        
    cur_minutes = utils.get_time_difference(now_d, start_time)/60
    cur_minutes -= game_config.player_robot['ai_update_delay_minute']

    if delay_days:
        open_day -= delay_days

    if delay_minutes:
        cur_minutes -= delay_minutes

    if c_did is None:
        c_did = '1_0'
    c_day = int(c_did.split('_')[0])
    c_minute = int(c_did.split('_')[1])

    use_data = None
    use_did = None
    use_dids = []
    for item in GatherPlayerData.query({'uid': gather_player.uid}, order_by='-time'):
        if c_did == item.did:
            break
        _open_day, _delay_minutes = map(int, item.did.split('_'))
        #可跨天更新
        if _open_day > open_day:
            continue
        if _open_day == open_day and _delay_minutes > cur_minutes:
            continue
        if _open_day > c_day or (_open_day == c_day and _delay_minutes > c_minute):
            use_dids.append(item.did)
    if use_dids:
        ai_update_max = game_config.player_robot.get('ai_update_max', 0)
        if ai_update_max == 0:
            use_did = use_dids[0]
        else:
            for i in range(ai_update_max)[::-1]:
                try:
                    use_did = use_dids[-(i+1)]
                    break
                except IndexError:
                    continue
    if use_did:
        data = GatherPlayerData.query({'uid': gather_player.uid, 'did': use_did})
        data = list(data)[0]
        use_data = data.data
    return {'did': use_did, 'data': use_data}

@jsonrpc_method('get_zone_list')
def get_zone_list(request, params):
    config_cache.judge()
    params = pickle.loads(params)
    pf = params['pf']
    pf_key = params['pf_key']
    if settings.WHERE in ['wx37', 'sg4_wx37']:
        pf_key = '%s|wx_37' % pf_key
    now = datetime.datetime.now()

    zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
    if zone_maintain_status == 1:
        service_zone_list = []
    else:
        maintain_config = CacheTable.get('zone_maintain_config', '{}')
        maintain_config = eval(str(maintain_config))
        service_zone_list = maintain_config.get('zone_list', [])
    full_maintain_status = CacheTable.get('full_maintain_status', 1)

    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    zone_list = []
    login_zone_list = []
    platform_time = None
    if user_zone_list:
        user_zone = user_zone_list[0]
        for k,v in sorted(user_zone.zone_login.items(),key=lambda x:x[1]['login_time'],reverse=True):
            if k not in game_config.zone:
                continue
            login_zone_list.append(k)
            zone_item = [k, game_config.zone[k][0], 1, 0]
            status = 0
            open_time = game_config.zone[k][2]
            if now >= open_time:
                if open_time + datetime.timedelta(minutes=game_config.system_simple['sever_new']) < now:
                    status = 1 #新
                else:
                    status = 2 #爆
            else:
                if open_time - datetime.timedelta(minutes=game_config.system_simple['sever_showtime']) < now:
                    status = 0 #预
            if game_config.zone[v['merge_zone']][8] > 0:
                merge = v['merge_zone']
                status = 3
            else:
                merge = ''
            zone_item.append(status)
            zone_item.append(settings.WHERE)
            zone_item.append(merge)
            maintain = 0 # 未维护
            if full_maintain_status == 0 or k in service_zone_list:
                maintain = 1 # 维护
            zone_item.append(maintain)
            zone_item.append(v['login_time'])
            zone_list.append(zone_item)
        platform_time = int(time.mktime(user_zone.add_time.timetuple()))

    zone_user_num = {}
    user_num_zone_list = []
    d = None
    has_login_zone = True
    if not zone_list:
        has_login_zone = False
        for k,v in sorted(game_config.zone.items(),  key=lambda x:x[1][2], reverse=True):
            zone_pf = game_config.zone_pf.get(k, None)
            if zone_pf:
                if zone_pf[0] and pf not in zone_pf[0]:
                    continue
                if zone_pf[1] and pf in zone_pf[1]:
                    continue
            if v[8]:
                continue
            if v[2] > now:
                continue
            if not v[1][0]:
                continue
            if d and v[2].date() != d.date():
                break
            d = v[2]
            user_num_zone_list.append(k)
    if len(user_num_zone_list) > 1:
        for zone in user_num_zone_list:
            user_count= CacheTable.get('unum%s' % zone, 0)
            zone_user_num['%s|%s' % (settings.WHERE, zone)] = user_count
    for k,v in sorted(game_config.zone.items(),  key=lambda x:x[1][2], reverse=True):
        zone_pf = game_config.zone_pf.get(k, None)
        if k in login_zone_list:
            continue
        if v[8] > 0:
            continue
        if not v[1][0]:
            continue
        if now >= v[2]:
            if v[2] + datetime.timedelta(minutes=game_config.system_simple['sever_new']) > now:
                status = 1 #新
            else:
                status = 2 #爆
            if v[3] == 0:
                forbid = game_config.system_simple['forbid_new_pf'].get(pf, game_config.system_simple['forbid_new'])
                if v[2] + datetime.timedelta(minutes=forbid) < now:
                    is_closed = 1
                else:
                    is_closed = 0
            else:
                is_closed = 0
        else:
            if v[2] - datetime.timedelta(minutes=game_config.system_simple['sever_showtime']) < now:
                status = 3 #预
                is_closed = 0
            else:
                status = 0
                is_closed = 1
        if game_config.system_simple['sever_only'] == 1:
            if is_closed == 1:
                continue
            if zone_pf:
                if zone_pf[0] and pf not in zone_pf[0]:
                    continue
                if zone_pf[1] and pf in zone_pf[1]:
                    continue
        maintain = 0 # 未维护
        if full_maintain_status == 0 or k in service_zone_list:
            maintain = 1 # 维护
        zone_item = [k, v[0], 0, is_closed, status, settings.WHERE, '', maintain, v[2]]
        zone_list.append(zone_item)

    return {
            'zone_list': zone_list,
            'has_login_zone': has_login_zone,
            'platform_time': platform_time,
            'zone_user_num': zone_user_num,
            }

def aes_encrypt(key, data):
    BS = AES.block_size
    pad = lambda s: s + (BS - len(s) % BS) * chr(BS - len(s) % BS)
    # cipher = AES.new(key)
    cipher = AES.new(key, AES.MODE_ECB)
    encrypted = cipher.encrypt(pad(data))
    result = base64.b64encode(encrypted)
    return result


def aes_decode(key, data):
    unpad = lambda s: s[0:-ord(s[-1])]
    cipher = AES.new(key, AES.MODE_ECB)
    result2 = base64.b64decode(data)
    decrypted = unpad(cipher.decrypt(result2))
    return decrypted

def get_qqgame_blue_vip_info(request, params):
    openid = params['openid']
    openkey = params['openkey']
    pf = params['my_pf']
    from libs.qqgame import QqGame
    api = QqGame(pf=pf)
    info = api.blue_vip_info(openid, openkey)
    return info

def get_h5_jj_access_token(request, params):
    """
    获取jj平台的access_token
    :param request:
    :param params:
            r_token:
    :return:
    """
    r_token = params['r_token']
    from libs.h5_5599 import Api
    api = Api()
    data = api.get_access_token(r_token)
    return data

def h5_5599_callback(request):
    """
    三国jj平台支付回调
    :param request:
    :return:
    """
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            if k in ['Extend', 'AppSchemeID', 'Sign']:
                continue
            req_params[k] = v
        if int(req_params['PayResult']) != 0:
            return HttpResponse('PayResult Error')
        from libs.h5_5599 import Api
        sign = request.REQUEST.get('Sign')
        api = Api()
        if sign != api.get_pay_sign(req_params):
            return HttpResponse('sign error')
        order_id = req_params['CPOrderID']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'success', req_params['SourceMoneyAmount'],
                            req_params['PID'], req_params['OrderID'], 'h5_5599', 'h5_5599')

    except:
        utils.print_err()
        return HttpResponse('success')

def get_h5_5599_order_sign(request, params):
    from libs.h5_5599 import Api
    api = Api()
    sign = api.get_order_sign(params)
    return {'sign': sign}

def h5_y5_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_y5 import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('sign error')
        order_id = json.loads(req_params['params'])['orderId']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', 'no_check',
                            'h5_y5', req_params['order_id'], 'h5_y5', 'h5_y5')

    except:
        utils.print_err()
        return HttpResponse('SUCCESS')

def h5_wakool_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_y5 import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('sign error')
        order_id = json.loads(req_params['params'])['orderId']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', 'no_check',
                            'h5_wakool', req_params['order_id'], 'h5_wakool', 'h5_wakool')

    except:
        utils.print_err()
        return HttpResponse('SUCCESS')

def h5_mengyou_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_mengyou import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('{"code": -1, "data": {}, "msg": "签名错误"}')
        order_id = req_params['dsn']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "data": {}, "msg": ""}', req_params['money'],
                            'h5_mengyou', req_params['osn'], 'h5_mengyou', 'h5_mengyou')

    except:
        utils.print_err()
        return HttpResponse('{"code": 1, "data": {}, "msg": ""}')

def h5_mengyou1_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_mengyou import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('{"code": -1, "data": {}, "msg": "签名错误"}')
        order_id = req_params['dsn']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "data": {}, "msg": ""}', req_params['money'],
                            'h5_mengyou1', req_params['osn'], 'h5_mengyou1', 'h5_mengyou1')

    except:
        utils.print_err()
        return HttpResponse('{"code": 1, "data": {}, "msg": ""}')

def get_h5_mengyou_sign(request, params):
    pid, zone, uid, ts = params['dsn'].split('|')
    pay_money = get_config_pay_money(pid)
    if int(pay_money) * 100 != params['money']:
        raise Exception('h5_mengyou apy money error')
    from libs.h5_mengyou import Api
    api = Api()
    sign = api.get_order_sign(params)
    return {'sign': sign}

def ad_mengyou_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_mengyou import Api
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('{"code": -1, "data": {}, "msg": "签名错误"}')
        order_id = req_params['dsn']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "data": {}, "msg": ""}', req_params['money'],
                            'ad_mengyou', req_params['osn'], 'ad_mengyou', 'ad_mengyou')

    except:
        utils.print_err()
        return HttpResponse('{"code": 1, "data": {}, "msg": ""}')

def ad_xiaoy_callback(request):
    try:
        req_params = json.loads(request.raw_post_data)
        from libs.ad_xiaoy import Api
        api = Api()
        if not api.verify_pay_sign(req_params):
            return HttpResponse('sign error')
        order_id = req_params['outTradeNo']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'success', req_params['totalFee'],
                            'ad_xiaoy', req_params['xiaoyTradeNo'], 'ad_xiaoy', 'ad_xiaoy')

    except:
        utils.print_err()
        return HttpResponse('success')

def h5_chaoyi_callback(request):
    """
    :param request:
    :return:
    """
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_chaoyi import Api
        api = Api()
        if not api.check_pay_sign(req_params):
            return HttpResponse('sign error')
        order_id = req_params['extension']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['goods_price']))*100,
                            'h5_chaoyi', req_params['order_num'], 'h5_chaoyi', 'h5_chaoyi')

    except:
        utils.print_err()
        return HttpResponse('SUCCESS')

def get_jj_h5_37_sign(request, params):
    from libs.h5_37 import Api
    api = Api('8274nh!8;HJ^55R2E7b^FSLVO(A#fl~')
    pid, zone, uid, ts = params['order_no'].split('|')
    pay_money = get_config_pay_money(pid)
    params['money'] = pay_money
    sign = api.sign(params)
    ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[
                     0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))

    return {'sign': sign, 'ip': ip}

def get_jj_yyjh_pay_sign(request, params):
    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')
    pay_money = get_config_pay_money(pid)
    if int(pay_money) != params['amt']:
        raise Exception('yyjh pay amount error')
    res = YiyouJH(tap=2).get_pay_code(params)

    return res

def get_jj_h5_7k_sign(request, params):
    from libs.h5_7k import Api7k
    pid, zone, uid, ts = params['order_id'].split('|')
    pay_money = get_config_pay_money(pid)
    params['amt'] = pay_money
    api = Api7k(pf='jj_h5_7k')
    code = api.get_save_code(params)
    return {'code': code}

def jj_h5_37_callback(request):
    try:
        from libs.h5_37 import Api
        api = Api('8274nh!8;HJ^55R2E7b^FSLVO(A#fl~')

        params = {}
        for k, v in request.POST.items():
            if k == 'fx_c_game_id':
                continue
            params[k] = v

        if api.sign(params) != params['sign']:
            return HttpResponse('{"code": -5, "msg": "", "data": ""}')

        order_id = params['order_no']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": "", "data": ""}', int(float(params['money'])) * 100, 'jj_h5_37',
                            params['order_id'], 'jj_h5_37', 'jj_h5_37')
    except:
        utils.print_err()
        return HttpResponse({"code": 1, "msg": "", "data": ""})

def jj_yyjh_callback(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        api = YiyouJH(tap=2)
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100, req_params['userid'], req_params['order'],
                            'jj_h5_yyjh', 'jj_h5_yyjh')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 0}')

def jj_h5_twyx_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = '4efab4e339bd2d049a2944827b43140c'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'jj_h5_twyx',
                            params['orderid'], 'jj_h5_twyx', 'jj_h5_twyx')
    except:
        utils.print_err()
        return HttpResponse('{"ret":1,"msg": "success"}')

def jj_h5_7k_callback(request):
    try:
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        from libs.h5_7k import Api7k
        api = Api7k(pf='jj_h5_7k')
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100, req_params['userid'],
                            req_params['7korder'], 'jj_h5_7k', 'jj_h5_7k')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 1}')

def jj_ad_caohua_callback(request):
    try:
        from libs.caohua import Api
        api = Api(pf='jj_ad_caohua')
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v


        if not api.verify_pay(params):
            return HttpResponse(' { "code" : 203, "msg":"签名校验失败", "data" :[] } ')

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{ "code" : 200, "msg":"成功", "data" :[] }', params['pay_amt'],
                            'jj_ad_caohua', params['orderno'], 'jj_ad_caohua', 'jj_ad_caohua')
    except:
        utils.print_err()
        return HttpResponse('{ "code" : 200, "msg":"成功", "data" :[] }')


def jj_h5_leyou_callback(request):
    from libs.leyou import LeYou
    api = LeYou(pf='jj_h5_leyou')

    params = {}
    for k, v in request.REQUEST.items():
        if v:
            params[k] = v

    if api.pay_sign(params) != params['sign']:
        return HttpResponse('{"result":0}')

    order_id = params['cporder']
    pid, zone, uid, ts = order_id.split('|')

    return add_user_pay(order_id, uid, zone, pid, '{"result":1}', int(float(params['amt'])) * 100, 'jj_h5_leyou', params['order'], 'jj_h5_leyou',
                        'jj_h5_leyou')

def jj_h5_mg1_callback(request):
    try:
        from libs.h5_mg import Api
        api = Api(pf='jj_h5_mg1')

        param_data = request.GET.get('param_data')
        sign = request.GET.get('sign')
        param_data = api.decrypt_param_data(param_data)

        if not api.verify_pay_sign(param_data, sign):
            return HttpResponse('verify error')

        param_data = urllib.unquote(param_data)
        params = {}
        for item in param_data.split('&'):
            k,v = item.split('=')
            params[k] = urllib.unquote(v)

        order_id = params['user_comment']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'jj_h5_mg1',
                            params['order_unique'], 'jj_h5_mg', 'jj_h5_mg1')
    except:
        utils.print_err()
        return HttpResponse('success')

def jj_h5_mg2_callback(request):
    try:
        from libs.h5_mg import Api
        api = Api(pf='jj_h5_mg2')

        param_data = request.GET.get('param_data')
        sign = request.GET.get('sign')
        param_data = api.decrypt_param_data(param_data)

        if not api.verify_pay_sign(param_data, sign):
            return HttpResponse('verify error')

        param_data = urllib.unquote(param_data)
        params = {}
        for item in param_data.split('&'):
            k,v = item.split('=')
            params[k] = urllib.unquote(v)

        order_id = params['user_comment']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'jj_h5_mg2',
                            params['order_unique'], 'jj_h5_mg', 'jj_h5_mg2')
    except:
        utils.print_err()
        return HttpResponse('success')

def muyou_ad_vn_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api()

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ad_vn', params['orderno'], 'muyou_ad_vn',
                            'muyou_ad_vn', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_ad_vn1_callback(request):
    """
    沐游官网安卓包
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api()

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ad_vn1', params['orderno'], 'muyou_ad_vn1',
                            'muyou_ad_vn1', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))


def cb_muyou_ad_vn_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='cb_muyou_ad_vn')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'cb_muyou_ad_vn', params['orderno'], 'cb_muyou_ad_vn',
                            'cb_muyou_ad_vn', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def cb_muyou_ad_vn1_callback1(request):
    """
    谷歌包 网页支付回调
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api, IOS_VN_MONEY_MAPS
        api = Api(pf='cb_muyou_ad_vn1')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        if params['extra'] == 'web_pay':
            pay_money = IOS_VN_MONEY_MAPS.get(pay_id)
        else:
            pay_money = int(game_config.system_simple['pay_money']['cb_muyou_ad_vn1'][pay_id])

        if pay_money != int(float(params['order_amt'])):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'cb_muyou_ad_vn1', params['orderno'], 'cb_muyou_ad_vn1',
                            'cb_muyou_ad_vn1', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))


def cb_muyou_ad_vn2_callback1(request):
    """
    本地包 游戏内支付回调
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='cb_muyou_ad_vn1')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        if params['extra'] == 'web_pay':
            pay_money = IOS_VN_MONEY_MAPS.get(pay_id)
        else:
            pay_money = int(game_config.system_simple['pay_money']['cb_muyou_ad_vn1'][pay_id])

        if pay_money != int(float(params['order_amt'])):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'cb_muyou_ad_vn2', params['orderno'], 'cb_muyou_ad_vn2',
                            'cb_muyou_ad_vn2', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def cb_muyou_ios_vn_callback1(request):
    """
    支付回调
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api, IOS_VN_MONEY_MAPS
        api = Api(pf='cb_muyou_ios_vn')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        if params['extra'] == 'web_pay':
            pay_money = IOS_VN_MONEY_MAPS.get(pay_id)
        else:
            pay_money = int(game_config.system_simple['pay_money']['cb_muyou_ios_vn'][pay_id])

        if pay_money != int(float(params['order_amt'])):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'cb_muyou_ios_vn', params['orderno'], 'cb_muyou_ios_vn',
                            'cb_muyou_ios_vn', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_hw_vn_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='muyou_hw_vn')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_hw_vn', params['orderno'], 'muyou_hw_vn',
                            'muyou_hw_vn', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_ios_vn_callback(request):
    """
    网站支付回调
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api, IOS_VN_MONEY_MAPS
        api = Api(pf='muyou_ios_vn')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        if params['extra'] == 'web_pay':
            pay_money = game_config.system_simple['pf_pay_money'].get(pay_id)
        else:
            pay_money = int(game_config.system_simple['pay_money']['muyou_ios_vn'][pay_id])

        if pay_money != int(float(params['order_amt'])):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ios_vn', params['orderno'], 'muyou_ios_vn',
                            'muyou_ios_vn', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))
    
def muyou_ios_vn_callback1(request):
    """
    原游戏内支付回调
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api, IOS_VN_MONEY_MAPS
        api = Api(pf='muyou_ios_vn')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        if params['extra'] == 'web_pay':
            pay_money = game_config.system_simple['pf_pay_money'].get(pay_id)
        else:
            pay_money = int(game_config.system_simple['pay_money']['muyou_ios_vn'][pay_id])

        if pay_money != int(float(params['order_amt'])):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ios_vn', params['orderno'], 'muyou_ios_vn',
                            'muyou_ios_vn', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))
    
def muyou_ios_vn1_callback(request):
    """
    网站支付回调
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='muyou_ios_vn1')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        if game_config.system_simple['pf_pay_money'].get(pid) != int(params['order_amt']):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ios_vn1', params['orderno'], 'muyou_ios_vn1',
                            'muyou_ios_vn1', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_ios_vn1_callback1(request):
    """
    原游戏内支付回调
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api, IOS_VN_MONEY_MAPS
        api = Api(pf='muyou_ios_vn1')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid

        if IOS_VN_MONEY_MAPS.get(pay_id) != int(params['order_amt']):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ios_vn1', params['orderno'], 'muyou_ios_vn1',
                            'muyou_ios_vn1', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_h5_vn1_callback(request):
    """
    :param request:
    :return:
    """
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='muyou_h5_vn1')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')
        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid

        if int(game_config.system_simple['pay_money']['muyou_h5_vn1'].get(pay_id)) != int(params['order_amt']):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error order_amt', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_h5_vn1', params['orderno'], 'muyou_h5_vn1', 'muyou_h5_vn1', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_pay_list(request):
    """
    沐游网页支付商品列表
    :param request:
    :return:
    """
    from libs.muyou import Api
    pf = request.GET['pf']
    req_params = {}
    for k,v in request.REQUEST.items():
        if k == 'ext':
            continue
        req_params[k] = v
    uid = req_params['cp_role_id']
    zone = req_params['server_id']
    uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(uid)
    if not user_zone:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'user not existed'}))
    if zone not in user_zone.zone_login:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'user not create role in %s' % zone}))
    api = Api(pf=pf)
    if not api.verify_pay_sign(sig_params):
        return HttpResponse(json.dumps({'msg': 'invalid sign', 'code': 302}))
    server_raise_msg = game_config.server_raise_msg_viet
    return_msg = game_config.return_msg_viet
    data = {}
    order_num = 1
    for pay_id, value in sorted(game_config.pay_config.iteritems(), key=lambda x:int(x[0][3:])):
        if pay_id not in EA_PAY_ID_MAPS:
            continue
        pay_num = int(pay_id[3:])
        if pay_num not in range(1, 9) and pay_num != 219:
            continue
        pay_desc = server_raise_msg.get(pay_id, 'Gold')
        data[str(order_num)] = {
            'product_desc': pay_desc,
            'cp_product_id': pay_id,
        }
        order_num += 1
    goods = []
    for goods_id, value in sorted(game_config.goods.iteritems(), key=lambda x:int(x[0][2:])):
        pay_id = value['pay_id']
        if pay_id not in EA_PAY_ID_MAPS:
            continue
        item = {
            'product_desc': return_msg.get(value['info'], value['info']),
            'cp_product_id': goods_id,
            'money': game_config.pay_config[pay_id][0]
        }
        goods.append(item)
    for item in sorted(goods, key=lambda x:x['money']):
        item.pop('money')
        data[str(order_num)] = item
        order_num += 1
    return HttpResponse(json.dumps({'code': 200, 'msg': '', 'data': data}))

def muyou_pay_sign(request, params):
    pf = params.pop('pf')
    from libs.muyou import Api
    api = Api(pf=pf)
    sign = api._get_sign(params)
    return {'sign': sign}

def get_muyou_sign(request,  params):
    from libs.muyou import Api
    pf = params['pf']
    st = params['at'] #login/pay
    data = params['data']
    api = Api(pf=pf)
    sign = api._get_sign(data, st)
    return {'sign': sign}

def muyou_create_order(request):
    """
    沐游手动创建订单接口
    :param request:
    :return:
    """
    from libs.muyou import Api, PID_VN_AD_MAPS, PID_TH_AD_MAPS, PID_TH_IOS_MAPS, PID_VN_IOS_MAPS, PID_CB_VN_MAPS, PID_VN_H5_MAPS
    product_id = request.GET['product_id']
    pf = request.GET['ext']
    if pf == 'muyou_ad_tl':
        PID_MAPS = PID_TH_AD_MAPS
    elif pf == 'muyou_ios_tl':
        PID_MAPS = PID_TH_IOS_MAPS
    elif pf == 'muyou_ios_vn1':
        PID_MAPS = PID_VN_IOS_MAPS
    elif pf == 'cb_muyou_ios_vn':
        PID_MAPS = PID_CB_VN_MAPS
    elif pf == 'cb_muyou_ad_vn1':
        PID_MAPS = PID_CB_VN_MAPS
    elif pf == 'cb_muyou_ad_vn2':
        PID_MAPS = PID_CB_VN_MAPS
    elif pf == 'muyou_h5_vn1':
        PID_MAPS = PID_VN_H5_MAPS
    else:
        PID_MAPS = PID_VN_AD_MAPS
    extra = request.GET.get('extra')
    if extra:
        if extra.startswith('gd'):
            pay_id = game_config.goods[extra]['pay_id']
        else:
            pay_id = extra
        if pay_id not in game_config.pay_config:
            return HttpResponse(json.dumps({'msg': 'wrongful product_id', 'code': 301}))
        pid = extra
    else:
        pid = PID_MAPS.get(product_id, None)
        if pid not in game_config.pay_config:
            return HttpResponse(json.dumps({'msg': 'wrongful product_id', 'code': 301}))
    sig_params = {}
    for k, v in request.GET.items():
        if k == 'ext':
            continue
        sig_params[k] = v

    api = Api(pf=pf)
    if not api.verify_pay_sign(sig_params):
        return HttpResponse(json.dumps({'msg': 'invalid sign', 'code': 302}))
    uid = sig_params['cp_role_id']
    zone = sig_params['server_id']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(old_uid)
    if not user_zone:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'user not existed'}))
    if zone not in user_zone.zone_login:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'user not create role in %s' % zone}))

    order_id = '%s|%s|%s|%s' % (pid, zone, uid, int(time.time()))
    res_data = {
        "cp_order_no": order_id,
        "extra": 'web_pay'
    }
    res_data['sign'] = api._get_sign(res_data)
    res = {
        "msg": "",
        "code": 200,
        "data": res_data
    }

    return HttpResponse(json.dumps(res))

def muyou_ad_tl_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='muyou_ad_tl')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['extra']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ad_tl', params['orderno'], 'muyou_ad_tl',
                            'muyou_ad_tl', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_hw_tl_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='muyou_hw_tl')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['extra']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_hw_tl', params['orderno'], 'muyou_hw_tl',
                            'muyou_hw_tl', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_ios_tl_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.muyou import Api
        api = Api(pf='muyou_ios_tl')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))

        order_id = params['extra']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'code': 200, 'msg': 'success', 'data': []}), 'no_check', 'muyou_ios_tl', params['orderno'], 'muyou_ios_tl',
                            'muyou_ios_tl', params['order_amt'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'code': 201, 'msg': '', 'data': []}))

def muyou_h5_tl_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.h5_muyou import Api
        api = Api()

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'status': 'error'}))

        order_id = params['trade_no']
        pid, zone, uid, ts = order_id.split('|')
        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid

        if game_config.system_simple['pf_pay_money'].get(pay_id) != int(float(params['amount'])):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error amount', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'status': 'success'}), 'no_check', 'muyou_h5_tl', params['out_trade_no'], 'muyou_h5_tl',
                            'muyou_h5_tl', params['amount'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'status': 'error'}))

def muyou_h5_vn_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        from libs.h5_muyou import Api
        api = Api(pf='muyou_h5_vn')

        if not api.verify_pay_sign(params):
            return HttpResponse(json.dumps({'status': 'error'}))

        order_id = params['trade_no']
        pid, zone, uid, ts = order_id.split('|')
        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid

        if int(game_config.system_simple['pay_money']['muyou_h5_vn'].get(pay_id)) != int(float(params['amount'])):
            return HttpResponse(json.dumps({'code': 201, 'msg': 'error amount', 'data': []}))

        return add_user_pay(order_id, uid, zone, pid, json.dumps({'status': 'success'}), 'no_check', 'muyou_h5_vn', params['out_trade_no'], 'muyou_h5_vn',
                            'muyou_h5_vn', params['amount'])
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'status': 'error'}))

def jj_mina_ios_ar_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        if int(params['orderStatus']) != 200:
            return HttpResponse('error orderStatus')
        from libs.mina import Api
        api = Api(pf='jj_mina_ios_ar')

        if not api.check_pay(params):
            return HttpResponse('error sign')

        order_id = params['callbackInfo']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'muyou_ios_vn', params['orderId'], 'jj_mina_ios_ar', 'jj_mina_ios_ar')
    except:
        utils.print_err()
        return HttpResponse('success')

def jj_mina_ad_ar_callback(request):
    try:
        params = {}
        for k,v in request.REQUEST.items():
            params[k] = v
        if int(params['orderStatus']) != 200:
            return HttpResponse('error orderStatus')
        from libs.mina import Api
        api = Api(pf='jj_mina_ad_ar')

        if not api.check_pay(params):
            return HttpResponse('error sign')

        order_id = params['callbackInfo']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'success', 'no_check', 'muyou_ios_vn', params['orderId'], 'jj_mina_ad_ar', 'jj_mina_ad_ar')
    except:
        utils.print_err()
        return HttpResponse('success')

def jj_h5_ch_callback(request):
    try:
        from libs.caohua import ApiH5_2
        api = ApiH5_2()
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        if not api.verify_pay(params):
            return HttpResponse(' { "code" : 203, "msg":"签名校验失败", "data" :[] } ')

        order_id = params['orderno_cp']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{ "code" : 200, "msg":"成功", "data" :[] }', params['pay_amt'], 'jj_h5_ch',
                            params['orderno'], 'jj_h5_ch', 'jj_h5_ch')
    except:
        utils.print_err()

@forward_sh_callback
def ea37_callback(request):
    try:
        from libs.ea37 import Api, EA_PAY_ID_MAPS
        api = Api(pf='ea37')
        req_params = params = json.loads(request.raw_post_data)
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 100, 'msg': '签名校验失败'}))
        order_id = req_params['remark']
        pid, zone, uid, ts, pay_from = order_id.split('|')
        product_id = req_params['productID']
        if product_id and pid in game_config.pay_config and product_id != EA_PAY_ID_MAPS.get(pid):
            return HttpResponse(json.dumps({'result': 0, 'code': 101, 'msg': 'Error Remark'}))

        ## 拆单或者额外返利赠送的黄金
        ext_coin = 0
        if req_params['coin']:
            ext_coin += req_params['coin']
        return add_user_pay(order_id, uid, zone, pid, json.dumps({'result': 1, 'code': 1, 'msg': 'success'}), 'no_check', req_params['currency'],
                            req_params['orderID'], pay_from, 'ea37', pf_pay_money=req_params.get('realMoney', req_params['money']), ext_coin=ext_coin)
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 102, 'msg': 'ServerError'}))

@forward_sh_callback
def tw37_callback(request):
    try:
        from libs.ea37 import Api, TW_PAY_ID_MAPS
        api = Api(pf='tw37')
        req_params = params = json.loads(request.raw_post_data)
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 100, 'msg': '签名校验失败'}))
        order_id = req_params['remark']
        pid, zone, uid, ts, pay_from = order_id.split('|')
        product_id = req_params['productID']
        if product_id and pid in game_config.pay_config and product_id != TW_PAY_ID_MAPS.get(pid):
            return HttpResponse(json.dumps({'result': 0, 'code': 101, 'msg': 'Error Remark'}))
        ## 拆单或者额外返利赠送的黄金
        ext_coin = 0
        if req_params['coin']:
            ext_coin += req_params['coin']
        return add_user_pay(order_id, uid, zone, pid, json.dumps({'result': 1, 'code': 1, 'msg': 'success'}), 'no_check', req_params['currency'],
                            req_params['orderID'], pay_from, 'tw37', pf_pay_money=req_params.get('realMoney', req_params['money']), ext_coin=ext_coin)
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 102, 'msg': 'ServerError'}))

def jj_junhai_gg_callback(request):
    try:
        from libs.junhai import Api
        api = Api(pf='jj_junhai_gg')
        req_params = {}
        for k,v in request.REQUEST.items():
            req_params[k] = v
        if req_params['pay_result'] == 0:
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'pay_result error', 'content': ''}))
        if not api.check_pay(req_params):
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'verify sign error', 'content': ''}))
        order_id = req_params['app_order_id']
        pid, zone, uid, ts = order_id.split('|')
        pay_money = int(req_params['total_fee'])
        if pay_money != int(game_config.system_simple['pf_pay_money'][pid]) * 100:
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'total_fee error', 'content': ''}))
        return add_user_pay(
            order_id=order_id,
            uid=uid,
            zone=zone,
            pid=pid,
            succ_info=json.dumps({'ret': 1, 'content': '', 'msg': ''}),
            pay_amount='no_check',
            pf_other='jj_junhai_gg',
            pf_order_id=req_params['order_id'],
            pay_from='jj_junhai_gg',
            pf='jj_junhai_gg'
        )
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'ret': 0, 'msg': 'error', 'content': ''}))

def jj_junhai_ios_callback(request):
    try:
        from libs.junhai import Api
        api = Api(pf='jj_junhai_ios')
        req_params = {}
        for k,v in request.REQUEST.items():
            req_params[k] = v
        if req_params['pay_result'] == 0:
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'pay_result error', 'content': ''}))
        if not api.check_pay(req_params):
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'verify sign error', 'content': ''}))
        order_id = req_params['app_order_id']
        pid, zone, uid, ts = order_id.split('|')
        pay_money = int(req_params['total_fee'])
        if pay_money != int(game_config.system_simple['pf_pay_money'][pid]) * 100:
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'total_fee error', 'content': ''}))
        return add_user_pay(
            order_id=order_id,
            uid=uid,
            zone=zone,
            pid=pid,
            succ_info=json.dumps({'ret': 1, 'content': '', 'msg': ''}),
            pay_amount='no_check',
            pf_other='jj_junhai_ios',
            pf_order_id=req_params['order_id'],
            pay_from='jj_junhai_ios',
            pf='jj_junhai_ios'
        )
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'ret': 0, 'msg': 'error', 'content': ''}))


def jj_junhai_os_callback(request):
    try:
        from libs.junhai import Api
        api = Api(pf='jj_junhai_os')
        req_params = {}
        for k,v in request.REQUEST.items():
            req_params[k] = v
        if req_params['pay_result'] == 0:
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'pay_result error', 'content': ''}))
        if not api.check_pay(req_params):
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'verify sign error', 'content': ''}))
        order_id = req_params['app_order_id']
        pid, zone, uid, ts = order_id.split('|')
        pay_money = int(req_params['total_fee'])
        if pay_money != int(game_config.system_simple['pf_pay_money'][pid]) * 100:
            return HttpResponse(json.dumps({'ret': 0, 'msg': 'total_fee error', 'content': ''}))
        return add_user_pay(
            order_id=order_id,
            uid=uid,
            zone=zone,
            pid=pid,
            succ_info=json.dumps({'ret': 1, 'content': '', 'msg': ''}),
            pay_amount='no_check',
            pf_other='jj_junhai_os',
            pf_order_id=req_params['order_id'],
            pay_from='jj_junhai_os',
            pf='jj_junhai_os'
        )
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'ret': 0, 'msg': 'error', 'content': ''}))

def cb_h5_37_callback(request):
    try:
        from libs.h5_37 import Api
        api = Api('g3Cth4SJkv6(XtY~v)3^3(7cnya9#4K3')

        ip_whitelist = game_config.system_simple.get('pay_callback_whitelist', {}).get('cn37')
        if ip_whitelist:
            if 'HTTP_X_FORWARDED_FOR' in request.META:
                user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
            else:
                user_ip = request.META.get('REMOTE_ADDR', '')
            if user_ip not in ip_whitelist:
                return HttpResponse(json.dumps({"code": -3, "msg": "", "data": ""}))

        params = {}
        for k, v in request.POST.items():
            if k == 'fx_c_game_id':
                continue
            params[k] = v

        if api.sign(params) != params['sign']:
            return HttpResponse('{"code": -5, "msg": "", "data": ""}')

        order_id = params['order_no']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": "", "data": ""}',
                            int(float(params['money'])) * 100, 'cb_h5_37', params['order_id'], 'cb_h5_37', 'cb_h5_37')
    except:
        utils.print_err()
        return HttpResponse(json.dumps({"code": -5, "msg": "", "data": ""}))

def cb_37_cn_callback(request):
    try:
        from libs.h5_37 import Api
        api = Api(pf='cb_37_h5')

        ip_whitelist = game_config.system_simple.get('pay_callback_whitelist', {}).get('cn37')
        if ip_whitelist:
            if 'HTTP_X_FORWARDED_FOR' in request.META:
                user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
            else:
                user_ip = request.META.get('REMOTE_ADDR', '')
            if user_ip not in ip_whitelist:
                return HttpResponse(json.dumps({"code": -3, "msg": "", "data": ""}))

        params = {}
        for k, v in request.POST.items():
            if k == 'fx_c_game_id':
                continue
            params[k] = v

        if not api.check_pay(params):
            return HttpResponse('{"code": -5, "msg": "", "data": ""}')

        order_id = params['order_no']
        pid, zone, uid, ts = order_id.split('|')
        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": "", "data": ""}',
                            int(float(params['money'])) * 100, 'cb_37_cn', params['order_id'], 'cb_37_cn', 'cb_37_cn')
    except:
        utils.print_err()
        return HttpResponse(json.dumps({"code": -5, "msg": "", "data": ""}))

def cb_ios_37_refund(request):
    """
    37赤壁 IOS包退款记录
    :param request:
    :return:
    """
    from libs.h5_37 import Api
    api = Api(pf='cb_37_ad')
    params = {}
    for k, v in request.POST.items():
        if k == 'fx_c_game_id':
            continue
        params[k] = v
    if api.sign(params) != params['sign']:
        return HttpResponse('{"code": -4, "msg": "", "data": ""}')
    order_id = params['order_no']
    pid, zone, uid, ts = order_id.split('|')
    onum = select([func.count(pay_records_table.c.id).label('cc')],
                  and_(pay_records_table.c.uid == uid, pay_records_table.c.zone == zone,
                       pay_records_table.c.order_id == order_id)).execute().fetchone()['cc']
    if onum <= 0:
        return HttpResponse(json.dumps({"code": -5, "msg": "订单不存在", "data": ""}))
    pay_records_table.update(and_(pay_records_table.c.uid == uid, pay_records_table.c.zone == zone,
                       pay_records_table.c.order_id == order_id)).execute(status=2)
    return HttpResponse(json.dumps({"code": 1, "msg": "", "data": ""}))

def cb_37_kr_callback(request):
    try:
        from libs.ea37 import Api, CB_KR_PAY_ID_MAPS
        api = Api(pf='cb_37_kr')
        req_params = params = json.loads(request.raw_post_data)
        if not api.verify_sign(req_params):
            return HttpResponse(json.dumps({'result': 0, 'code': 100, 'msg': '签名校验失败'}))
        order_id = req_params['remark']
        pid, zone, uid, ts, pay_from = order_id.split('|')
        product_id = req_params['productID']
        if product_id and product_id in game_config.pay_config and product_id != EA_PAY_ID_MAPS.get(pid):
            return HttpResponse(json.dumps({'result': 0, 'code': 101, 'msg': 'Error Remark'}))

        ## 拆单或者额外返利赠送的黄金
        ext_coin = 0
        if req_params['coin']:
            ext_coin += req_params['coin']
        return add_user_pay(order_id, uid, zone, pid, json.dumps({'result': 1, 'code': 1, 'msg': 'success'}), 'no_check', req_params['currency'],
                            req_params['orderID'], pay_from, 'cb_37_kr', pf_pay_money=req_params['money'], ext_coin=ext_coin)
    except:
        utils.print_err()
        return HttpResponse(json.dumps({'result': 0, 'code': 102, 'msg': 'ServerError'}))

def cb_ad_just4fun_callback(request):
    try:
        from libs.just4fun import Api
        params = {}
        for k, v in request.POST.items():
            params[k] = v
        api = Api()
        if not api.verify_sign(params):
            return HttpResponse('SIGN ERROR')
        order_id = params['cpOrderNo']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS',
                            'no_check', 'cb_ad_just4fun', params['orderNo'], 'cb_ad_just4fun', 'cb_ad_just4fun')
    except:
        utils.print_err()
        return HttpResponse('ERROR')


def cb_h5_yyjh_callback(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        api = YiyouJH(tap=4)
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100,
                            req_params['userid'], req_params['order'],
                            'cb_h5_yyjh', 'cb_h5_yyjh')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 0}')

def cb_h5_yyjh_cn_callback(request):
    try:
        req_params = {}
        for k, v in request.POST.items():
            req_params[k] = v
        api = YiyouJH(tap=5)
        if not api.check_pay_sign(req_params):
            return HttpResponse('{"Result": 0}')
        order_id = req_params['cporder']
        pid, zone, uid, ts = order_id.split('|')
        succ_info = '{"Result": 1}'

        return add_user_pay(order_id, uid, zone, pid, succ_info, int(float(req_params['amt'])) * 100,
                            req_params['userid'], req_params['order'],
                            'cb_h5_yyjh_cn', 'cb_h5_yyjh_cn')
    except:
        utils.print_err()
        return HttpResponse('{"Result": 0}')

def cb_h5_mojie_callback(request):
    try:
        from libs.h5_mojie import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api()
        if not api.check_pay(req_params):
            return HttpResponse('{"code": 102, "msg": "签名错误"}')
        order_id = req_params['Cp_orderid']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"code": 1, "msg": "发货成功"}', 'no_check',
                            'cb_h5_mojie', req_params['Orderid'],
                            'cb_h5_mojie', 'cb_h5_mojie')
    except:
        utils.print_err()
        return HttpResponse('{"code": 0, "msg": "系统错误"}')

def muyou_bind_gift(request):
    """
    沐游绑定礼包
    :param request:
    :return:
    """
    from libs.muyou import Api
    pf = request.GET.get('pf', 'muyou_ad_vn')
    api = Api(pf=pf)
    params = {}
    for k,v in request.REQUEST.items():
        if k == 'pf':
            continue
        params[k] = v
    if not api.verify_gift_sign(params):
        return HttpResponse(json.dumps({'code': 203, 'msg': 'failed sign', 'data': []}))
    uid = params['role_id']
    zone = params['server_id']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(old_uid)
    if not user_zone:
        return HttpResponse(json.dumps({'code': 202, 'msg': 'user not existed', 'data': []}))

    data = CacheTable.get('muyou_bind_gift_%s' % old_uid, None)
    if data:
        return HttpResponse(json.dumps({'code': 202, 'msg': 'repeat gift', 'data': []}))
    muyou_config = game_config.system_simple['muyou_bind_tel']
    UserZone.call_server_api(user_zone.zone_login[zone]['merge_zone'],
                             'admin_gift_msg',
                             {'name_info': {'cn': [game_config.server_raise_msg_viet[muyou_config['mail_name']], game_config.server_raise_msg_viet[muyou_config['mail_info']]]},
                              'gift_dict': muyou_config['reward'],
                              'uids': [uid]
                              })
    CacheTable.set('muyou_bind_gift_%s' % old_uid, 'send_gift', 60*60*24*30*36)
    return HttpResponse(json.dumps({'code': 200, 'msg': 'success', 'data': []}))

def muyou_gift_bag(request):
    """
    沐游礼包
    :param request:
    :return:
    """
    params = {}
    for k,v in request.REQUEST.items():
        if k == 'pf':
            continue
        params[k] = v
    from libs.muyou import Api
    pf = request.REQUEST['pf']
    api = Api(pf=pf)

    if not api.verify_pay_sign(params):
        return HttpResponse(json.dumps({'code': 203, 'msg': u'签名校验失败', 'data': []}))
    uid = params['cp_role_id']
    zone = params['server_id']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(old_uid)
    if not user_zone:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'用户不存在', 'data': []}))
    if zone not in user_zone.zone_login:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'用户未在%s区创建角色' % zone, 'data': []}))
    gift_bag_config = game_config.system_simple['muyou_gift_bag']
    if pf not in gift_bag_config:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'pf参数错误', 'data': []}))
    product_id = params['product_id']
    if product_id not in gift_bag_config[pf]:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'计费点不存在', 'data': []}))
    gift_bag = gift_bag_config[pf][product_id]
    now = datetime.datetime.now()
    if gift_bag['type'] == 'bd':
        if CacheTable.get('muyou_bind_gift_%s_%s' % (old_uid, pf), None):
            return HttpResponse(json.dumps({'code': 202, 'msg': u'绑定礼包已领取', 'data': []}))
        UserZone.call_server_api(user_zone.zone_login[zone]['merge_zone'],
                                 'admin_gift_msg',
                                 {'name_info': {'cn': [game_config.server_raise_msg_viet[gift_bag_config['bd_mail_name']], game_config.server_raise_msg_viet[gift_bag_config['bd_mail_info']]]},
                                  'gift_dict': gift_bag['reward'],
                                  'uids': [uid]
                                  })
        CacheTable.set('muyou_bind_gift_%s_%s' % (old_uid, pf), product_id, 60*60*24*30*12*100)
    elif gift_bag['type'] == 'tq':
        if CacheTable.get('muyou_tq_gift_%s_%s_%s%s' % (old_uid, pf, now.year, now.month), None):
            return HttpResponse(json.dumps({'code': 202, 'msg': u'特权礼包已领取', 'data': []}))
        UserZone.call_server_api(user_zone.zone_login[zone]['merge_zone'],
                                 'admin_gift_msg',
                                 {'name_info': {'cn': [game_config.server_raise_msg_viet[gift_bag_config['tq_mail_name']], game_config.server_raise_msg_viet[gift_bag_config['tq_mail_info']]]},
                                  'gift_dict': gift_bag['reward'],
                                  'uids': [uid]
                                  })
        CacheTable.set('muyou_tq_gift_%s_%s_%s%s' % (old_uid, pf, now.year, now.month), product_id, 60*60*24*30*12*100)
    elif gift_bag['type'] == 'pr':
        if CacheTable.get('muyou_pr_gift_%s' % old_uid, None):
            return HttpResponse(json.dumps({'code': 202, 'msg': u'预注册奖励已领取', 'data': []}))
        UserZone.call_server_api(user_zone.zone_login[zone]['merge_zone'],
                                 'admin_gift_msg',
                                 {'name_info': {'cn': [game_config.server_raise_msg_viet[gift_bag_config['pr_mail_name']], game_config.server_raise_msg_viet[gift_bag_config['pr_mail_info']]]},
                                  'gift_dict': gift_bag['reward'],
                                  'uids': [uid]
                                  })
        CacheTable.set('muyou_pr_gift_%s' % old_uid, product_id, 60*60*24*30*12*100)
    return HttpResponse(json.dumps({'code': 200, 'msg': 'success', 'data': []}))

def muyou_pre_registe(request):
    """
    沐游谷歌预注册奖励
    :param request:
    :return:
    """
    params = {}
    for k,v in request.REQUEST.items():
        if k == 'pf':
            continue
        params[k] = v
    from libs.muyou import Api
    pf = request.REQUEST['pf']
    api = Api(pf=pf)

    if not api.verify_pay_sign(params):
        return HttpResponse(json.dumps({'code': 203, 'msg': u'签名校验失败', 'data': []}))
    uid = params['cp_role_id']
    zone = params['server_id']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(old_uid)
    if not user_zone:
        return HttpResponse(json.dumps({'code': 202, 'msg': u'用户不存在', 'data': []}))

def cb_r2g_pre_register(request):
    """
    r2韩国预注册奖励
    """
    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_kr')
    if not api.check_sign(params, stype='pay'):
        return HttpResponse('{"status":"6","message": "错误的sign"}')

    uid = params['roleid']
    zone = params['serverid']
    uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(uid)
    if not user_zone:
        return HttpResponse('{"status":"101","message": "用户不存在"}')
    if zone not in user_zone.zone_login:
        return HttpResponse('{"status":"102","message": "用户未创角"}')
    gift_cache_key = '%spre_register_gift_%s' % (settings.CACHE_PRE, user_zone.uid)
    if CacheTable.get(gift_cache_key, default=False):
        return HttpResponse('{"status":"103","message": "奖励已发放"}')
    pre_register_config = game_config.system_simple['pre_register_reward']
    if user_zone.pf not in pre_register_config[2]:
        return HttpResponse('{"status":"104","message": "没有奖励"}')
    pf_lan = game_config.system_simple['server_raise_pf'].get(user_zone.pf, None)
    if pf_lan:
        return_msg_config = getattr(game_config, 'server_raise_msg_%s' % pf_lan)
    else:
        return_msg_config = game_config.server_raise_msg
    mail_name = return_msg_config[pre_register_config[0]]
    mail_info = return_msg_config[pre_register_config[1]]

    UserZone.call_server_api(zone,
                    'admin_gift_msg',
                    {'name_info': {'cn': [mail_name, mail_info]},
                     'gift_dict': pre_register_config[3],
                     'uids': [user_zone.uid]
                     })
    CacheTable.set(gift_cache_key, True, 60*60*24*30*12*100)

def cb_r2g_send_gift(request, params):
    uid = params['uid']
    zone = params['zone']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(uid)
    if not user_zone:
        return {'status': 'error', 'msg': u'用户不存在'}
    if CacheTable.get('cb_r2g_rate_reward_%s' % old_uid):
        return {'status': 'error', 'msg': u'奖励已领取'}
    gift_dict = game_config.system_simple['rate_reward']
    mail_name = game_config.server_raise_msg_kr['r2g_rate_mail_name']
    mail_info = game_config.server_raise_msg_kr['r2g_rate_mail_info']
    UserZone.call_server_api(zone,
                    'admin_gift_msg',
                    {'name_info': {'cn': [mail_name, mail_info]},
                     'gift_dict': gift_dict,
                     'uids': [user_zone.uid]
                     })
    CacheTable.set('cb_r2g_rate_reward_%s' % old_uid, 'send_gift', 60*60*24*365*20)
    return {'status': 'success'}

def wx_bugu_callback(request):
    try:
        from libs.bugu import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api()
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'wx_bugu', req_params['order_id'],
                            'wx_bugu', 'wx_bugu')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')
def h5_bugu_callback(request):
    try:
        from libs.bugu import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api(pf='h5_bugu')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'h5_bugu', req_params['order_id'],
                            'h5_bugu', 'h5_bugu')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def cb_wx_bugu_callback(request):
    try:
        from libs.bugu import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api(pf='cb_wx_bugu_1')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'cb_wx_bugu', req_params['order_id'],
                            'cb_wx_bugu', 'cb_wx_bugu')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def cb_h5_bugu_callback(request):
    try:
        from libs.bugu import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api(pf='cb_h5_bugu')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'cb_h5_bugu', req_params['order_id'],
                            'cb_h5_bugu', 'cb_h5_bugu')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def sg4_wx_bugu_callback(request):
    try:
        from libs.bugu import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api(pf='sg4_wx_bugu1')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'sg4_wx_bugu', req_params['order_id'],
                            'sg4_wx_bugu', 'sg4_wx_bugu')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def sg4_h5_bugu_callback(request):
    try:
        from libs.bugu import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api(pf='sg4_h5_bugu')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'sg4_h5_bugu', req_params['order_id'],
                            'sg4_h5_bugu', 'sg4_h5_bugu')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def sg4_h5_bugu1_callback(request):
    try:
        from libs.bugu_h5 import Api
        appid = request.REQUEST['appid']
        sign = request.REQUEST['sign']
        data = request.REQUEST['data']
        data = json.loads(data)
        api = Api(pf='sg4_h5_bugu1')
        if not api.verify_pay_sign(appid, sign, data):
            return HttpResponse('FAILURE')
        order_id = data['cpOrder']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(data['money']))*100, 'sg4_h5_bugu1', data['orderId'], 'sg4_h5_bugu1', 'sg4_h5_bugu1')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def sg4_wx_bugu2_callback(request):
    try:
        from libs.bugu_h5 import Api
        appid = request.REQUEST['appid']
        sign = request.REQUEST['sign']
        data = request.REQUEST['data']
        data = json.loads(data)
        api = Api(pf='sg4_wx_bugu2')
        if not api.verify_pay_sign(appid, sign, data):
            return HttpResponse('FAILURE')
        order_id = data['cpOrder']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(data['money']))*100, 'sg4_wx_bugu2', data['orderId'], 'sg4_wx_bugu2', 'sg4_wx_bugu2')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')


def wxv3_h5_order(request, params):
    """
    微信支付v3版本创建h5订单, 爱评乐玩
    :param request:
    :param params:
    :return:
    """
    from libs.weixin_v3 import Api
    pid = params['pid']
    zone = params['zone']
    uid = params['uid']
    pay_pf = params['pf']   ##aipinglewan  赤壁级以后新游戏使用
    pay_money = get_config_pay_money(pid)
    if 'HTTP_X_FORWARDED_FOR' in request.META:
        user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
    else:
        user_ip = request.META.get('REMOTE_ADDR', '')
    notify_url = '%s/wxv3_callback/' % settings.BASE_URL
    #api = Api('aipinglewan')
    #api = Api('zhangyoulewan')
    api = Api('youwpz')
    ts = str(int(time.time()))[2:]
    order_id = '%s|%s|%s|%s' % (uid, pid, zone, ts)
    data = api.create_h5_order(str(order_id), pay_money, user_ip, pay_pf.encode('utf-8'), notify_url)
    h5_url = data['h5_url']
    #redirect_url = 'game.aipinglewan.com://'
    redirect_url = 'game.zhangylw.com://'
    mweb_url = '%s&redirect_url=%s' % (h5_url, urllib.quote_plus(redirect_url))
    headers = {'CLIENT-IP': user_ip, 'X-FORWARDED-FOR': user_ip}
    req = urllib2.Request(mweb_url, headers=headers)
    req.headers['X-FORWARDED-FOR'] = user_ip
    req.headers['CLIENT-IP'] = user_ip
    #req.headers['Referer'] = 'game.aipinglewan.com'
    req.headers['Referer'] = 'game.zhangylw.com'
    req_res = urllib2.urlopen(req).read()
    s = req_res.find('weixin://wap/pay?')
    req_res = req_res[s:]
    e = req_res.find('"')
    pay_url = req_res[:e]

    return pay_url

def wxv3_callback(request):
    """
    微信支付v3版本  支付通知回调
    :param request:
    :return:
    """
    try:
        from libs.weixin_v3 import Api
        body = request.raw_post_data
        sig_string = '%s\n%s\n%s\n' % (request.META['HTTP_WECHATPAY_TIMESTAMP'], request.META['HTTP_WECHATPAY_NONCE'], body)
        signature = request.META['HTTP_WECHATPAY_SIGNATURE']
        serial_no = request.META['HTTP_WECHATPAY_SERIAL']
        #api = Api('aipinglewan')
        #api = Api('zhangyoulewan')
        api = Api('youwpz')
        if not api.rsa_verify(serial_no, sig_string, signature):
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        params = json.loads(body)
        if params['resource_type'] != 'encrypt-resource':
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        resource = params.get('resource')
        if not resource:
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        algorithm = resource.get('algorithm')
        if algorithm != 'AEAD_AES_256_GCM':
            raise Exception('sdk does not support this algorithm')
        nonce = resource['nonce']
        ciphertext = resource['ciphertext']
        associated_data = resource.get('associated_data', '')
        data = api.aes_decrypt(nonce, ciphertext, associated_data)
        if not data:
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        data = json.loads(data)

        order_id = data['out_trade_no']
        uid, pid, zone, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', data['amount']['total'], 'wxv3_aplw', data['transaction_id'], 'wxv3_aplw',
                            'wxv3_aplw')
    except:
        utils.print_err()

def gzsgz_wxv3_order(request, params):
    """
    微信支付v3版本创建jsapi订单,
    :param request:
    :param params:
    :return:
    """
    from libs.weixin_v3 import Api
    pid = params['pid']
    zone = params['zone']
    uid = params['uid']
    openid = params['openid']
    pay_money = get_config_pay_money(pid)
    if 'HTTP_X_FORWARDED_FOR' in request.META:
        user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
    else:
        user_ip = request.META.get('REMOTE_ADDR', '')
    notify_url = '%s/gzsgz_wxv3_callback/' % settings.BASE_URL
    api = Api('wx_gzsgz')
    ts = str(int(time.time()))[2:]
    order_id = '%s|%s|%s|%s' % (uid, pid, zone, ts)
    data = api.create_jsapi_order(str(order_id), pay_money, user_ip, 'wx_gzsgz', openid, notify_url)
    return data

def gzsgz_wxv3_callback(request):
    """
    微信支付v3版本  支付通知回调
    :param request:
    :return:
    """
    try:
        from libs.weixin_v3 import Api
        body = request.raw_post_data
        sig_string = '%s\n%s\n%s\n' % (request.META['HTTP_WECHATPAY_TIMESTAMP'], request.META['HTTP_WECHATPAY_NONCE'], body)
        signature = request.META['HTTP_WECHATPAY_SIGNATURE']
        serial_no = request.META['HTTP_WECHATPAY_SERIAL']
        api = Api('wx_gzsgz')
        if not api.rsa_verify(serial_no, sig_string, signature):
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        params = json.loads(body)
        if params['resource_type'] != 'encrypt-resource':
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        resource = params.get('resource')
        if not resource:
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        algorithm = resource.get('algorithm')
        if algorithm != 'AEAD_AES_256_GCM':
            raise Exception('sdk does not support this algorithm')
        nonce = resource['nonce']
        ciphertext = resource['ciphertext']
        associated_data = resource.get('associated_data', '')
        data = api.aes_decrypt(nonce, ciphertext, associated_data)
        if not data:
            return HttpResponse('{"code": "FAIL", "message": "失败"}', status=500)
        data = json.loads(data)

        order_id = data['out_trade_no']
        uid, pid, zone, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', data['amount']['total'], 'wx_gzsgz', data['transaction_id'], 'wx_gzsgz',
                            'wx_gzsgz')
    except:
        utils.print_err()

def cb_wx_twyx1_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        from libs.twyx import Api

        api = Api(pf='cb_wx_twyx1')
        if not api.check_pay(params):
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'cb_wx_twyx1', params['orderid'], 'cb_wx_twyx1', 'cb_wx_twyx1')
    except:
        utils.print_err()

def cb_wx_twyx2_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        from libs.twyx import Api

        api = Api(pf='cb_wx_twyx2')
        if not api.check_pay(params):
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'cb_wx_twyx2', params['orderid'], 'cb_wx_twyx2', 'cb_wx_twyx2')
    except:
        utils.print_err()


def cb_h5_twyx1_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = 'b9cc429c36f6e628610d2fd2e8cdb919'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'cb_h5_twyx1', params['orderid'], 'cb_h5_twyx1', 'cb_h5_twyx1')
    except:
        utils.print_err()

def cb_h5_twyx2_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v

        key = '4fac50e10f2058c90e0cd364855faca0'
        if hashlib.md5(params['uid'] + params['money'] + params['time'] + params['sid'] + params['orderid'] + params['ext'] + key).hexdigest() != params['sign']:
            return HttpResponse('{"ret":0,"msg": "错误的sign"}')

        order_id = params['ext']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"ret":1,"msg": "success"}', int(float(params['money'])) * 100, 'cb_h5_twyx2', params['orderid'], 'cb_h5_twyx2', 'cb_h5_twyx2')
    except:
        utils.print_err()

def cb_wx_zhangwan_callback(request):
    try:
        params = json.loads(request.raw_post_data)
        pf = params['order']['ext']
        from libs.zhangwan import Api
        api = Api(pf=pf)
        if not api.check_pay(params):
            return HttpResponse(json.dumps({'status_code': 0, 'msg': "错误的sign"}))

        order_id = params['order']['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, json.dumps({"status_code":1,"msg": "success"}), int(float(params['order']['amount'])), 'cb_wx_zhangwan', params['order']['order_id'], 'cb_wx_zhangwan', 'cb_wx_zhangwan')
    except:
        utils.print_err()

def cb_ad_zhangwan1_callback(request):
    try:
        params = request.raw_post_data
        sign = request.GET['sign']
        from libs.zhangwan import Api
        api = Api(pf='cb_ad_zhangwan1')
        if not api.af_check_pay(sign, params):
            return HttpResponse('ERROR')
        req_params = json.loads(params)

        order_id = req_params['vorder_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS',
                int(float(req_params['fee']))*100, 'cb_wx_zhangwan1', req_params['sn'],
                'cb_ad_zhangwan1', 'cb_ad_zhangwan1')
    except:
        utils.print_err()

def cb_ad_zhangwan2_callback(request):
    try:
        params = request.raw_post_data
        sign = request.GET['sign']
        from libs.zhangwan import Api
        api = Api(pf='cb_ad_zhangwan2')
        if not api.af_check_pay(sign, params):
            return HttpResponse('ERROR')
        req_params = json.loads(params)

        order_id = req_params['vorder_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS',
                int(float(req_params['fee']))*100, 'cb_ad_zhangwan2', req_params['sn'],
                'cb_ad_zhangwan2', 'cb_ad_zhangwan2')
    except:
        utils.print_err()



def cb_wx_yiyang_callback(request):
    try:
        from libs.yiyang import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api(pf='cb_wx_yiyang_1')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'cb_wx_yiyang', req_params['order_id'],
                            'cb_wx_yiyang', 'cb_wx_yiyang')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def cb_h5_yiyang_callback(request):
    try:
        from libs.yiyang import Api
        req_params = {}
        for k, v in request.REQUEST.items():
            req_params[k] = v
        api = Api(pf='cb_h5_yiyang')
        if not api.verify_pay_sign(req_params):
            return HttpResponse('FAILURE')
        order_id = req_params['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, 'SUCCESS', int(float(req_params['product_price']))*100,
                            'cb_h5_yiyang', req_params['order_id'],
                            'cb_h5_yiyang', 'cb_h5_yiyang')
    except:
        utils.print_err()
        return HttpResponse('FAILURE')

def receive_fight_data(request, params):
    """
    接收前端打过来的js测试战斗数据
    """
    uid = params['uid']
    req_data = params['req_data']
    rsp_data = params.get('rsp_data')
    CallInterfaceRecord.save_record(uid, req_data, rsp_data)
    return {'status': 'success'}

def get_test_fight_data(request, params):
    cid = params['id']
    call = CallInterfaceRecord.get(cid)
    if not call:
        data = {'req_data': {}, 'rsp_data': {}}
    else:
        data = {'rsp_data': call.rsp_data, 'req_data': call.req_data}
    return {'status': 'success', 'data': data}
    
@forward_sh_callback
def fs_wx_37_callback(request):
    try:
        from libs.zsh_37 import Api

        # ip_whitelist = game_config.system_simple.get('pay_callback_whitelist', {}).get('cn37')
        # if ip_whitelist:
        #     if 'HTTP_X_FORWARDED_FOR' in request.META:
        #         user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0]
        #     else:
        #         user_ip = request.META.get('REMOTE_ADDR', '')
        #     if user_ip not in ip_whitelist:
        #        return HttpResponse(json.dumps({"code": -3, "msg": "", "data": ""}))

        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        pf = params['dext']
        api = Api(pf=pf)

        if not api.check_pay(params):
            return HttpResponse('{"state": 0, "msg": "签名错误", "data": ""}')

        order_id = params['doid']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid, '{"state": 1, "msg": "", "data": ""}',
                            int(float(params['money'])) * 100, 'fs_wx_37', params['oid'], 'fs_wx_37', 'fs_wx_37')
    except:
        utils.print_err()
        return HttpResponse('{"state": 0, "msg": "系统错误", "data": ""}')

def cb_r2g_zone_list(request):
    
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_kr')
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': []}))
    data = []
    now = datetime.datetime.now()
    for k,v in sorted(game_config.zone.items(), key=lambda x:x[1][2]):
        if v[2] > now:
            continue
        if v[4] == 3:
            continue
        if v[8] == 0:
            data.append({'serverid': k, 'servername': v[0], 'status': 'enabled'})
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': data}))

def cb_r2g_role_info(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_kr')
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    username = params['username']
    roleid = params['roleid']
    zone = params['serverid']
    if roleid:
        user_zone = UserZone.get(roleid)
    else:
        pf_key = '%s|cb_r2g_kr' % username
        user_zone = list(UserZone.query({'pf_key': pf_key}))
        user_zone = user_zone[0] if user_zone else None
    if not user_zone:
        return HttpResponse(json.dumps({'status': 2, 'msg': 'fail', 'data': {}}))
    new_uid = UserZone.get_new_uid(user_zone.uid, zone)

    data = cache.get('cb_r2g_role_info_%s' % new_uid, None)
    if not data:
        app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
        data = {
            'username': username,
            'rolename': app_user['uname'],
            'roleid': new_uid,
            'level': app_user['home']['building001']['lv']
            }
        cache.set('cb_r2g_role_info_%s' % new_uid, data, 300)
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': data}))

def cb_r2g_pay_list(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_kr')
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    data = [
        'pay1',
        'pay2',
        'pay3',
        'pay4',
        'pay5',
        'pay6',
        'pay7',
        'pay8',
        ]
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': {'productids': data}}))

def cb_r2g_create_roder(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_kr')
    if not api.check_sign(params, stype='pay'):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    uid = params['roleid']
    zone = params['serverid']
    pid = params['productid']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(old_uid)
    if not user_zone:
        return HttpResponse(json.dumps({'status': 2, 'msg': 'fail', 'data': {}}))
    order_id = '%s|%s|%s|%s' % (pid, zone, old_uid, int(time.time()))
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': {'transactionid': order_id}}))

def cb_r2g_kr_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        from libs.r2game import Api
        api = Api(pf='cb_r2g_kr')
        if not api.check_sign(params, stype='pay'):
            return HttpResponse('{"status":"6","message": "错误的sign"}')

        order_id = params['gamecno']
        pid, zone, uid, ts = order_id.split('|')
        pay_from = params.get('extra_data', 'cb_ad_r2_gg')
        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        pf_pay_money = int(game_config.system_simple['pay_money'][pay_from][pay_id])

        return add_user_pay(order_id, uid, zone, pid, '{"status":"0","message": ""}', 'no_check', 'cb_r2g_kr', params['orderid'], pay_from, 'cb_r2g_kr', pf_pay_money=pf_pay_money)
    except:
        utils.print_err()

def cb_r2g_hk_callback(request):
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        from libs.r2game import Api
        api = Api(pf='cb_r2g_hk')
        if not api.check_sign(params, stype='pay'):
            return HttpResponse('{"status":"6","message": "错误的sign"}')

        order_id = params['gamecno']
        pid, zone, uid, ts = order_id.split('|')
        pay_from = params.get('extra_data', 'cb_r2_hk')
        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        pf_pay_money = float(game_config.system_simple['pay_money'][pay_from][pay_id])

        return add_user_pay(order_id, uid, zone, pid, '{"status":"0","message": ""}', 'no_check', 'cb_r2g_hk', params['orderid'], pay_from, 'cb_r2g_hk', pf_pay_money=pf_pay_money)
    except:
        utils.print_err()

def cb_r2g_hk_zone_list(request):
    
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_hk')
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': []}))
    data = []
    now = datetime.datetime.now()
    for k,v in sorted(game_config.zone.items(), key=lambda x:x[1][2]):
        if v[2] > now:
            continue
        if v[4] == 3:
            continue
        if v[8] == 0:
            data.append({'serverid': k, 'servername': v[0], 'status': 'enabled'})
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': data}))

def cb_r2g_hk_role_info(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_hk')
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    username = params['username']
    roleid = params['roleid']
    zone = params['serverid']
    if roleid:
        user_zone = UserZone.get(roleid)
    else:
        pf_key = '%s|cb_r2g_hk' % username
        user_zone = list(UserZone.query({'pf_key': pf_key}))
        user_zone = user_zone[0] if user_zone else None
    if not user_zone:
        return HttpResponse(json.dumps({'status': 2, 'msg': 'fail', 'data': {}}))
    new_uid = UserZone.get_new_uid(user_zone.uid, zone)

    data = cache.get('cb_r2g_role_info_%s' % new_uid, None)
    if not data:
        app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
        data = {
            'username': username,
            'rolename': app_user['uname'],
            'roleid': new_uid,
            'level': app_user['home']['building001']['lv']
            }
        cache.set('cb_r2g_role_info_%s' % new_uid, data, 300)
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': data}))

def cb_r2g_hk_pay_list(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_hk')
    if not api.check_sign(params):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    data = [
        'pay1',
        'pay2',
        'pay3',
        'pay4',
        'pay5',
        'pay6',
        'pay7',
        'pay8',
        ]
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': {'productids': data}}))

def cb_r2g_hk_create_roder(request):
    params = {}
    for k,v in request.GET.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_hk')
    if not api.check_sign(params, stype='pay'):
        return HttpResponse(json.dumps({'status': 1, 'msg': 'fail', 'data': {}}))
    uid = params['roleid']
    zone = params['serverid']
    pid = params['productid']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(old_uid)
    if not user_zone:
        return HttpResponse(json.dumps({'status': 2, 'msg': 'fail', 'data': {}}))
    order_id = '%s|%s|%s|%s' % (pid, zone, old_uid, int(time.time()))
    return HttpResponse(json.dumps({'status': 0, 'msg': 'success', 'data': {'transactionid': order_id}}))

def cb_r2g_hk_pre_register(request):
    """
    r2韩国预注册奖励
    """
    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    from libs.r2game import Api
    api = Api(pf='cb_r2g_hk')
    if not api.check_sign(params, stype='pay'):
        return HttpResponse('{"status":"6","message": "错误的sign"}')

    uid = params['roleid']
    zone = params['serverid']
    uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(uid)
    if not user_zone:
        return HttpResponse('{"status":"101","message": "用户不存在"}')
    if zone not in user_zone.zone_login:
        return HttpResponse('{"status":"102","message": "用户未创角"}')
    gift_cache_key = '%spre_register_gift_%s' % (settings.CACHE_PRE, user_zone.uid)
    if CacheTable.get(gift_cache_key, default=False):
        return HttpResponse('{"status":"103","message": "奖励已发放"}')
    pre_register_config = game_config.system_simple['pre_register_reward']
    if user_zone.pf not in pre_register_config[2]:
        return HttpResponse('{"status":"104","message": "没有奖励"}')
    pf_lan = game_config.system_simple['server_raise_pf'].get(user_zone.pf, None)
    if pf_lan:
        return_msg_config = getattr(game_config, 'server_raise_msg_%s' % pf_lan)
    else:
        return_msg_config = game_config.server_raise_msg
    mail_name = return_msg_config[pre_register_config[0]]
    mail_info = return_msg_config[pre_register_config[1]]

    UserZone.call_server_api(zone,
                    'admin_gift_msg',
                    {'name_info': {'cn': [mail_name, mail_info]},
                     'gift_dict': pre_register_config[3],
                     'uids': [user_zone.uid]
                     })
    CacheTable.set(gift_cache_key, True, 60*60*24*30*12*100)

def cb_fy_hk_freeze_user(request):
    params = json.loads(request.raw_post_data)
    key = 'cc1e2e0320179d23a1459c14d42f5ecb'
    uid = params['playerId']
    zone = params['zoneId']
    user_id = params['userId']
    server_id = params['serverId']
    time = params['time'] # 分钟
    sign = params['sign']
    nickname = params['nickname']
    content = params['content']
    vip_lv = params['vipLevel']
    sign_str = ''.join([key, uid, user_id, zone, server_id, str(time)])
    _sign = hashlib.md5(sign_str).hexdigest()
    if sign != _sign:
        return HttpResponse(json.dumps({'code': 1, 'message': u'签名验证失败'}))
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(old_uid)
    if not user_zone:
        return HttpResponse(json.dumps({'code': 2, 'message': u'用户不存在'}))
    ban_hours = int(time)*1.0/60
    freeze_msg = game_config.server_raise_msg['freeze_chat']
    freeze_user(old_uid, zone, 'feiyou', 2, freeze_msg, hour=ban_hours)
    # 企业微信推送消息
    # https://developer.work.weixin.qq.com/document/path/91770#%E6%96%87%E6%9C%AC%E7%B1%BB%E5%9E%8B
    try:
        webhook = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=91dbd546-266a-4b20-9ee1-e22fb2b1fa89'
        webhook_params = {
                'msgtype': 'text',
                'text': {
                    'content': u'''《三國志Origin》天眼禁言提醒。
    区服ID：%s
    角色ID：%s
    角色名：%s
    VIP等级：%s
    禁言时间：%s
    聊天内容：%s
    ''' % (zone, old_uid, nickname, vip_lv, str(time), content),
                    }
                }
        requests.post(webhook, json=webhook_params, timeout=5)
    except:
        utils.print_err()
    return HttpResponse(json.dumps({'code': 0, 'message': u'success'}))

def cb_fy_hk_callback(request):
    """
    线上支付回调
    """
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        from libs.feiyou import Api
        api = Api()
        if not api.check_pay1(params):
            return HttpResponse('{"code":"0","message": "error sign"}')

        order_id = params['cp_order_id']
        pid, zone, uid, ts, pf = order_id.split('|')
        if pid.startswith('gd'):
            pay_id = game_config.goods[pid]['pay_id']
        else:
            pay_id = pid
        pf_pay_money = float(game_config.system_simple['pay_money'][pf][pay_id])
        if float(params['cp_price']) != pf_pay_money:
            return HttpResponse('{"code":"0","message": "error cp_price"}')
        ext_coin = int(params['extra_stone'])

        return add_user_pay(order_id, uid, zone, pid,
                '{"code":"1","message":"","data":"OK"}', 'no_check',
                'cb_fy_hk', params['order_id'], 'cb_fy_hk', 'cb_fy_hk',
                pf_pay_money=pf_pay_money, ext_coin=ext_coin)
    except:
        utils.print_err()

def cb_fy_hk_web_callback(request):
    """
    网页支付回调
    """
    try:
        params = {}
        for k, v in request.REQUEST.items():
            params[k] = v
        print datetime.datetime.now(), " - cb_fy_hk_web_callback - ", params
        from libs.feiyou import Api, PID_MAPS
        api = Api()
        if not api.check_pay2(params):
            return HttpResponse('{"code":"0","message": "error sign"}')
	user_id = params['user_id']
        zone = params['server_code']
        product_id = params['product_id']
	pf_key = '%s|cb_fy_hk' % user_id
	user_zone_list = list(UserZone.query({'pf_key': pf_key}))
	if not user_zone_list:
            return HttpResponse('{"code":"0","message": "not user"}')
	user_zone = user_zone_list[0]
        pay_id = PID_MAPS.get(product_id, None)
        if pay_id is None:
            return HttpResponse('{"code":"0","message": "error product_id"}')

        pf_pay_money = float(game_config.system_simple['pay_money'][user_zone.pf][pay_id])
        if float(params['cp_price']) != pf_pay_money:
            return HttpResponse('{"code":"0","message": "error cp_price"}')
        order_id = '%s|%s|%s|%s|%s' % (pay_id, zone, user_zone.uid, int(time.time()), user_zone.pf)
        ext_coin = int(params['extra_stone'])

        return add_user_pay(order_id, user_zone.uid, zone, pay_id,
                '{"code":"1","message":"","data":"OK"}', 'no_check',
                'cb_fy_hk_web', params['order_id'], 'cb_fy_hk_web', 'cb_fy_hk',
                pf_pay_money=pf_pay_money, ext_coin=ext_coin)
    except:
        utils.print_err()

def cb_fy_hk_register_rewards(request):
    """
    飞游预注册奖励发放
    """
    prams = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    from libs.feiyou import Api
    api = Api()
    if not api.check_registe(params):
        return HttpResponse('{"code":"0","message": "error sign"}')
    user_id = params['user_id']
    zone = params['server_code']
    pf_key = '%s|cb_fy_hk' % user_id
    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone_list:
        return HttpResponse('{"code":"0","message": "not user"}')
    user_zone = user_zone_list[0]
    if zone not in user_zone.zone_login:
        return HttpResponse('{"code":"0","message": "error server_code"}')

    pre_register_config = game_config.system_simple['pre_register_reward']
    gift_cache_key = '%spre_register_gift_%s' % (settings.CACHE_PRE, user_zone.uid)
    if CacheTable.get(gift_cache_key, default=False):
        return HttpResponse('{"code":"0","message": "repeate"}')
    if user_zone.pf not in pre_register_config[2]:
        return HttpResponse('{"code":"0","message": "not reward"}')
    mail_name = game_config.server_raise_msg[pre_register_config[0]]
    mail_info = game_config.server_raise_msg[pre_register_config[1]]
    UserZone.call_server_api(zone,
                    'admin_gift_msg',
                    {'name_info': {'cn': [mail_name, mail_info]},
                     'gift_dict': pre_register_config[3],
                     'uids': [user_zone.uid]
                     })
    CacheTable.set(gift_cache_key, True, 60*60*24*365*100)
    return HttpResponse('{"code":"1","message": "success"}')

def cb_fy_hk_get_roles(request):
    """
    飞游角色查询
    """
    params = {}
    for k, v in request.REQUEST.items():
        params[k] = v
    from libs.feiyou import Api
    api = Api()
    sign = params.pop('sign')
    params['cp_sign'] = sign
    if not api.check_login(params):
        return HttpResponse('{"code":"0","message": "error sign"}')
    user_id = params['user_id']
    pf_key = '%s|cb_fy_hk' % user_id
    user_zone_list = list(UserZone.query({'pf_key': pf_key}))
    if not user_zone_list:
        return HttpResponse('{"code":"0","message": "not user"}')
    user_zone = user_zone_list[0]
    data = []
    for zone in user_zone.zone_login.keys():
        _user = cache.get('feiyou_role_%s_%s' % (user_zone.uid, zone), None)
        if not _user:
            app_user = UserZone.call_server_api(zone, 'get_user', {'uid': user_zone.uid})
            _user = {
                    'server_code': zone,
                    'role_id': UserZone.get_new_uid(user_zone.uid, zone),
                    'role_name': app_user['uname'],
                    'role_level': app_user['home']['building001']['lv']
                    }
            cache.set('feiyou_role_%s_%s' % (user_zone.uid, zone), data, 300)
        data.append(_user)
    return HttpResponse(json.dumps({'code': 1, 'data': data}))



def report_dawanka_pay(request, params):
    uid = params['uid']
    zone = params['zone']
    old_uid = UserZone.get_old_uid(uid, zone)
    user_zone = UserZone.get(uid)
    openid,_ = user_zone.pf_key.split('|')
    openkey = user_zone.pf_pwd
    from libs.qqgame import QqGame
    api = QqGame(pf=user_zone.pf)
    data = api.report_dawanka_pay(openid, openkey)
    return data

def fs_wx_zhangwan_callback(request):
    try:
        params = json.loads(request.raw_post_data)
        pf = params['order']['ext']
        from libs.zhangwan import Api
        api = Api(pf=pf)
        if not api.check_pay(params):
            return HttpResponse(json.dumps({'status_code': 0, 'msg': "错误的sign"}))

        order_id = params['order']['cp_order_id']
        pid, zone, uid, ts = order_id.split('|')

        return add_user_pay(order_id, uid, zone, pid,
                json.dumps({"status_code":1,"msg": "success"}),
                int(float(params['order']['amount'])), 'fs_wx_zhangwan',
                params['order']['order_id'], 'fs_wx_zhangwan', 'fs_wx_zhangwan')
    except:
        utils.print_err()

def update_sensitive_words(request):
    from libs.ea37 import Api
    pf = request.GET['pf'] # ea37  tw37
    params = json.loads(request.raw_post_data)

    api = Api(pf)
    status = api.update_sensitive_words(params)
    CacheTable.set('ban_words_version', str(int(time.time())), 60*60*24*30*12*100)
    return HttpResponse(json.dumps({'result': status, 'msg': 'success'}))

def get_sensitive_words(request):
    from libs.ea37 import Api
    pf = request.GET['pf'] # ea37  tw37
    params = json.loads(request.raw_post_data)

    api = Api(pf)
    data = api.get_local_sensitive_words(params)
    result = {'data': data, 'msg': 'success'}
    if data:
        result['result'] = 1
    else:
        result['result'] = 0
    return HttpResponse(json.dumps(result))

def gift_pack_send(request):
    """
    通用礼包发放接口
    """
    if settings.WHERE == 'cb37':
        print "gift_pack_send>>: ", request.REQUEST.items()
        from libs.h5_37 import Api
        api = Api(pf='cb_37_h5')
        user_id = request.REQUEST['user_id']
        server_id = request.REQUEST['server_id']
        role_id = request.REQUEST['role_id']
        gift_id = request.REQUEST['gift_id']
        order_id = request.REQUEST['order_id']
        _time = request.REQUEST['time']
        sign = request.REQUEST['sign']
        now = datetime.datetime.now()
        if gift_id not in game_config.system_simple['gift_pack']:
            return HttpResponse(json.dumps({'code': 20005, 'msg': u'礼包不存在', 'data': {}}))
        s,e = game_config.system_simple['gift_pack'][gift_id]['period']
        if now < s or now > e:
            return HttpResponse(json.dumps({'code': 20006, 'msg': u'礼包不在有效期内', 'data': {}}))
        user_zone = UserZone.get(role_id)
        if not user_zone:
            return HttpResponse(json.dumps({'code': 10002, 'msg': u'角色不存在', 'data': {}}))
        zone = parse_37_sid(server_id)
        _sign_str = '%s%s%s%s%s%s%s' % (user_id, server_id, role_id, gift_id, order_id, _time, api.sign_key)
        _sign = hashlib.md5(_sign_str).hexdigest()
        if sign != _sign:
            return HttpResponse(json.dumps({'code': 10003, 'msg': u'签名错误', 'data': {}}))
        if game_config.zone[zone][8] > 0:
            role_id = UserZone.get_new_uid(role_id, zone)
        msg_name, msg_info = game_config.system_simple['gift_pack'][gift_id]['msg']
        mail_name = game_config.server_raise_msg[msg_name]
        mail_info = game_config.server_raise_msg[msg_info]
        reward = game_config.system_simple['gift_pack'][gift_id]['reward']
        UserZone.call_server_api(zone,
                             'admin_gift_msg',
                             {'name_info': {'cn': [mail_name, mail_info]},
                              'gift_dict': reward,
                              'uids': [role_id]
                              })
        return HttpResponse(json.dumps({'code': 1, 'msg': u'发放成功', 'data': {}}))
    else:
        return HttpResponse('error')

def ucoin_recharge(request):
    """
    代币充值
    """
    if settings.WHERE == 'cb37':
        from libs.h5_37 import Api
        api = Api(pf='cb_37_h5')
        user_id = request.REQUEST['user_id']
        server_id = request.REQUEST['server_id']
        role_id = request.REQUEST['role_id']
        order_id = request.REQUEST['order_id']
        _time = request.REQUEST['time']
        money = request.REQUEST['money']
        sign = request.REQUEST['sign']
        now = datetime.datetime.now()
        onum = select([func.count(pay_records_table.c.id).label('cc')],
                  and_(pay_records_table.c.pf_order_id == order_id)).execute().fetchone()['cc']
        if onum > 0:
            return HttpResponse(json.dumps({'code': 20003, 'msg': u'订单重复, 已充值成功', 'data': {}}))
        user_zone = UserZone.get(role_id)
        if not user_zone:
            return HttpResponse(json.dumps({'code': 10002, 'msg': u'角色不存在', 'data': {}}))
        _sign_str = '%s%s%s%s%s%s%s' % (user_id, server_id, role_id, order_id, _time, money, api.sign_key)
        _sign = hashlib.md5(_sign_str).hexdigest()
        if sign != _sign:
            return HttpResponse(json.dumps({'code': 10003, 'msg': u'签名错误', 'data': {}}))
        ucoin_num, ratio = game_config.system_simple['ucoin_recharge'][str(money)]
        ucoin_num += round(ucoin_num*ratio)
        # 不再直接增加ucoin，改为通过游戏服务器发放代金券
        # user_zone.ucoin += ucoin_num
        user_zone.pay_money += int(money)
        zone = parse_37_sid(server_id)
        order_values = {'zone': user_zone.zone_login[zone]['merge_zone'], 'old_zone': zone,
                'uid': old_uid, 'order_id': order_id, 'pay_coin': ucoin_num,
                    'pay_money': money, 'pf_pay_money': money, 'pay_time': now,
                    'pf_other': 'ucoin',
                    'pf_order_id': order_id, 'pay_from': user_zone.pf, 
                    'pf': user_zone.pf, 'status': 1, 'country': -1, 'lv': 1,
                    'power': 0, 'goods_id': 'ucoin_recharge'}
        last_id = pay_records_table.insert(values=order_values).execute().last_inserted_ids()[0]
        pay_result = UserZone.call_server_api(zone, 'ucoin_recharge', {'uid': role_id, 'money': money}, retry=5)
        if pay_result['succ'] == 1:
            country = pay_result['country']
            power = pay_result['power']
            lv = pay_result['lv']
            pay_records_table.update(and_(pay_records_table.c.id == last_id)).execute(country=country, lv=lv, power=power)
            user_zone.save()
            return HttpResponse(json.dumps({'code': 1, 'msg': u'成功', 'data': {}}))
        else:
            return HttpResponse(json.dumps({'code': 20003, 'msg': u'充值失败', 'data': {}}))

    else:
        return HttpResponse('error')



def jsonGateway(request):
    try:
        if_b64 = request.GET.get('e', 0)
        if int(if_b64) != 0:
            raw_post_data = request.raw_post_data
            aes_key = game_config.system_simple['aes_key'][str(if_b64)]
            client_data = json.loads(aes_decode(aes_key, raw_post_data))
        else:
            client_data = json.loads(request.raw_post_data)
        method = client_data.get('method', None)
        params = client_data.get('params')
        if method == 'sys.get_config':
            return_code, data = api_view.Sys.get_config(client_data)
        elif method == 'sys.new_get_config':
            data = new_get_config(client_data)
        elif method == 'sys.new_get_config_v1':
            data = new_get_config_v1(client_data)
        elif method == 'user_zone.register':
            data = UserZone.register(request, params)
        elif method == 'user_zone.register_fast':
            data = UserZone.register_fast(request, params)
        elif method == 'user_zone.send_login_sign':
            data = UserZone.send_login_sign(request, params)
        elif method == 'user_zone.login':
            data = UserZone.login(request, params, client_data['phone_id'])
        elif method == 'user_zone.validate_user_code':
            data = UserZone.validate_user_code(request, params)
        elif method == 'user_zone.set_zones':
            data = UserZone.set_zones(request, params)
        elif method == 'user_zone.send_sign':
            data = UserZone.send_sign(request, params)
        elif method == 'user_zone.tel_bind':
            data = UserZone.tel_bind(request, params)
        elif method == 'user_zone.fb_google_bing':
            data = UserZone.fb_google_bind(request, params)
        elif method == 'bug_msg.get_bug_msg':
            data = BugMsg.get_bug_msg(request, params)
        elif method == 'bug_msg.send_bug_msg':
            data = BugMsg.send_bug_msg(request, params)
        elif method == 'user_zone.pay':
            data = zfb_pay(request, params)
        elif method == 'user_zone.yyb_pay':
            data = yyb_pay(request, params)
        elif method == 'user_zone.ios_pay':
            data = ios_pay(request, params)
        elif method == 'user_zone.get_xh_sign':
            data = get_xh_sign(request, params)
        elif method == 'user_zone.get_hw_sign':
            data = get_hw_sign(request, params)
        elif method == 'user_zone.get_hw_tw_sign':
            data = get_hw_tw_sign(request, params)
        elif method == 'user_zone.get_mz_sign':
            data = get_mz_sign(request, params)
        elif method == 'user_zone.get_uc_sign':
            data = get_uc_sign(request, params)
        elif method == 'user_zone.gg_callback':
            data = gg_callback(request, params)
        elif method == 'user_zone.gg_callback_v3':
            data = gg_callback_v3(request, params)
        elif method == 'user_zone.cb_gg_callback':
            data = cb_gg_callback(request, params)
        elif method == 'user_zone.get_h5_37_sign':
            data = get_h5_37_sign(request, params)
        elif method == 'user_zone.get_wx_37_sign':
            data = get_wx_37_sign(request, params)
        elif method == 'user_zone.get_ios_37_sign':
            data = get_ios_37_sign(request, params)
        elif method == 'user_zone.get_h5_7k_sign':
            data = get_h5_7k_sign(request, params)
        elif method == 'user_zone.get_h5_bg_sign':
            data = get_h5_bg_sign(request, params)
        elif method == 'user_zone.get_37_ios_sign':
            data = get_37_ios_sign(request, params)
        elif method == 'user_zone.get_vivo_sign':
            data = get_vivo_sign(request, params)
        elif method == 'user_zone.get_oppo_sign':
            data = get_oppo_sign(request, params)
        elif method == 'user_zone.weixin_app_pay':
            data = weixin_app_pay(request, params)
        elif method == 'user_zone.get_weixin_share':
            data = get_weixin_share(request, params)
        elif method == 'user_zone.wxv3_h5_order':
            data = wxv3_h5_order(request, params)
        elif method == 'user_zone.gzsgz_wxv3_order':
            data = gzsgz_wxv3_order(request, params)
        elif method == 'user_zone.get_yyjh_pay_sign':
            data = get_yyjh_pay_sign(request, params)
        elif method == 'user_zone.get_yyjh2_pay_sign':
            data = get_yyjh2_pay_sign(request, params)
        elif method == 'user_zone.get_yyjh_hk_pay_sign':
            data = get_yyjh_hk_pay_sign(request, params)
        elif method == 'user_zone.get_cb_h5_yyjh_pay_sign':
            data = get_cb_h5_yyjh_pay_sign(request, params)
        elif method == 'user_zone.get_cb_h5_yyjh_cn_pay_sign':
            data = get_cb_h5_yyjh_cn_pay_sign(request, params)
        elif method == 'user_zone.wx_h5_pay':
            data = wx_h5_pay(request, params)
        elif method == 'user_zone.zfb_h5_pay':
            data = zfb_h5_pay(request, params)
        elif method == 'user_zone.yyb_pay_from':
            data = yyb_pay_from(request, params)
        elif method == 'user_zone.yyb_install_from':
            data = yyb_install_from(request, params)
        elif method == 'user_zone.install_code':
            data = InstallCode.install_request(request, params, client_data['phone_id'])
        elif method == 'user_zone.get_h5_360_sign':
            data = get_h5_360_sign(request, params)
        elif method == 'user_zone.get_h5_360_2_sign':
            data = get_h5_360_2_sign(request, params)
        elif method == 'user_zone.get_h5_360_3_sign':
            data = get_h5_360_3_sign(request, params)
        elif method == 'user_zone.get_h5_muzhi_sign':
            data = get_h5_muzhi_sign(request, params)
        elif method == 'user_zone.get_h5_muzhi2_sign':
            data = get_h5_muzhi2_sign(request, params)
        elif method == 'user_zone.get_awy_bi_url':
            data = get_awy_bi_url(request, params)
        elif method == 'user_zone.get_fengkuang_bi_url':
            data = get_fengkuang_bi_url(request, params)
        elif method == 'user_zone.get_h5_qq_prepay_id':
            data = get_h5_qq_prepay_id(request, params)
        elif method == 'user_zone.get_qqgame_blue':
            data = get_qqgame_blue_vip_info(request, params)
        elif method == 'user_zone.get_h5_wende_sign':
            data = get_h5_wende_sign(request, params)
        elif method == 'user_zone.get_h5_7477_sign':
            data = get_h5_7477_sign(request, params)
        elif method == 'user_zone.get_h5_5599_order_sign':
            data = get_h5_5599_order_sign(request, params)
        elif method == 'user_zone.get_h5_mengyou_sign':
            data = get_h5_mengyou_sign(request, params)
        elif method == 'user_zone.get_jj_access_token':
            data = get_h5_jj_access_token(request, params)
        elif method == 'user_zone.h5_qqdt_buy_goods':
            data = h5_qqdt_buy_goods(request, params)
        elif method == 'user_zone.h5_qqdt3_buy_goods':
            data = h5_qqdt3_buy_goods(request, params)
        elif method == 'user_zone.cb_h5_qqdt_buy_goods':
            data = cb_h5_qqdt_buy_goods(request, params)
        elif method == 'user_zone.h5_qqdt_buy_goods_m':
            data = h5_qqdt_buy_goods_m(request, params)
        elif method == 'user_zone.h5_qqdt3_buy_goods_m':
            data = h5_qqdt3_buy_goods_m(request, params)
        elif method == 'user_zone.cb_h5_qqdt_buy_goods_m':
            data = cb_h5_qqdt_buy_goods_m(request, params)
        elif method == 'user_zone.get_jj_h5_37_sign':
            data = get_jj_h5_37_sign(request, params)
        elif method == 'user_zone.get_jj_yyjh_pay_sign':
            data = get_jj_yyjh_pay_sign(request, params)
        elif method == 'user_zone.get_jj_h5_7k_sign':
            data = get_jj_h5_7k_sign(request, params)
        elif method == 'user_zone.get_month_pay_records':
            data = get_user_pay_records(request, params)
        elif method == 'user_zone.hw_pay_token_verify':
            data = hw_pay_token_verify(request, params)
        elif method == 'user_zone.hw_tw_pay_token_verify':
            data = hw_tw_pay_token_verify(request, params)
        elif method == 'user_zone.get_cb_h5_37_sign':
            data = get_cb_h5_37_sign(request, params)
        elif method == 'user_zone.get_cb_37_cn_sign':
            data = get_cb_37_cn_sign(request, params)
        elif method == 'user_zone.ucoin_pay':
            data = UserZone.ucoin_pay(request, params)
        elif method == 'user_zone.get_you77_h5_jp_sign':
            data = get_you77_h5_jp_sign(request, params)
        elif method == 'user_zone.cb_ad_mycard_auth_global':
            data = cb_ad_mycard_auth_global(request, params)
        elif method == 'user_zone.cb_ad_mycard_verify':
            data = cb_ad_mycard_verify(request, params)
        elif method == 'user_zone.log_pay':
            ## 前端打印支付前参数接口
            print "[%s] - befor_user_pay_log - %s" % (str(datetime.datetime.now()), params)
            data = {}
        elif method == 'test.receive_fight_data':
            # 接收后台JS测试战斗数据
            data = receive_fight_data(request, params)
        elif method == 'test.get_fight_data':
            # 接收后台JS测试战斗数据
            data = get_test_fight_data(request, params)
        elif method == 'user_zone.sync_invitation':
            data = InvitationRecord.sync_invitation(request, params)
        elif method == 'user_zone.muyou_pay_sign':
            data = muyou_pay_sign(request, params)
        elif method == 'user_zone.get_muyou_sign':
            data = get_muyou_sign(request, params)
        elif method == 'user_zone.report_dawanka_pay':
            data = report_dawanka_pay(request, params)
        elif method == 'user_zone.cb_r2g_send_gift':
            data = cb_r2g_send_gift(request, params)
        elif method == 'user_zone.get_user_ip_addr':
            data = UserZone.get_user_ip_addr(request, params)

        res = {'method': client_data.get('method'), 'params': client_data.get('params'), 'return_code': 0, 'data': data,
               'server_now': int(time.time() * 1000), }

        json_res = json.dumps(res, default=Serializer.json_default)
        if int(if_b64) != 0:
            aes_key = game_config.system_simple['aes_key'][str(if_b64)]
            json_res = aes_encrypt(aes_key, json_res)
        res = HttpResponse(json_res)
        res['Access-Control-Allow-Origin'] = '*'
        return res
    except:
        utils.print_err()
        raise

def get_user_pay_records(request, params):
    """
    获取用户当前月的充值金额
    :param uid:
    :return:
    """
    uid = params['uid']
    start_at, stop_at = utils.get_current_month_date_range()
    pay_record = select([func.sum(ASP.c.pay_money)], and_(ASP.c.uid == uid,
                                                           ASP.c.pay_time >= start_at,
                                                           ASP.c.pay_time <= stop_at)).execute().fetchall()
    total_money = pay_record[0][0]
    return {'total_money': total_money}

def get_zone_thirty_days_pay_records(zone):
    """
    获取指定区最近30天内的充值记录
    :param zone:
    :return:
    """
    data = {}
    stop_at = datetime.datetime.now()
    start_at = datetime.datetime(*(stop_at.date() - datetime.timedelta(days=30)).timetuple()[:6])
    pay_records = select([ASP.c.uid, ASP.c.old_zone, ASP.c.pay_money], and_(ASP.c.pay_time >= start_at,
                                                                            ASP.c.pay_time <= stop_at,
                                                                            ASP.c.zone == zone)).execute().fetchall()
    for uid, old_zone, pay_money in pay_records:
        uid_zone = '%s|%s' % (uid, old_zone)
        data.setdefault(uid_zone, 0)
        data[uid_zone] += int(pay_money)
    return data

def get_zone_ids(merge_index, zone_range):
    zones = []
    for start, stop in zone_range:
        zones += range(start, stop+1)
    if merge_index == 0:
        return map(lambda x: str(x), zones)
    else:
        return map(lambda x: 'h%s_%s' % (merge_index, x), zones)

def get_last_merge_zone(zone):
    """
    计算合区最新的起始ID
    :param zone:
    :return:
    """
    zone_list = game_config.help_msg['zone_merge']
    for zone_ranges in zone_list.values():
        merge_index = None
        for _merge_index, zone_range in enumerate(zone_ranges):
            zone_ids = get_zone_ids(_merge_index, zone_range)
            if zone not in zone_ids:
                continue
            merge_index = _merge_index + 1
            break
        if merge_index is None:
            continue
        for merge_zone in get_zone_ids(merge_index, zone_ranges[merge_index]):
            if merge_zone in game_config.zone.keys():
                continue
            return merge_zone, zone_ranges[merge_index]
    return None, None

@utils.auth_required
def get_zone_country_data(request):
    """

    :param request:
            zones: ['h1_2', 'h1_1']
    :return:
        country_data: 国家数据
            [
            {
                'zone': '',         #区ID
                'open_days': '',    #开服天数
                'country': '',      #国家
                'capital_num': 0,   #都城
                'city_num': 0,      #城池
                'powerful_num': 0,  #活跃高战
                'active_num': 0,    #活跃中坚
                'rank_score': 0,    #排名积分
                'top_power': 0,     #尖端战力
                'active_power_sum': 0,  #活跃总战力
                'active_pay': 0,    #活跃实际充值
                'active_pay_fake': 0,   #活跃虚拟充值
                'active_coin': 0,   #活跃coin
                'multiple_power': 0,    #综合国力
                'top20_country_credit': 0,         #前20战功和
                'country_active': 0,    #国家活跃
                'normal_num': 0,    #活跃低战
                'best_hero_power': 0, #单将最高战力
                'thirty_pay_money': 0, #30天活跃充值
            },]
    """
    now = datetime.datetime.now()
    if request.method == 'GET':
        return HttpResponse(json.dumps({'state':'error'}))
    params = json.loads(request.raw_post_data)
    zones = params['zones']
    # 计算本次合区的起始ID
    start_zone, zone_range = get_last_merge_zone(zones[0])
    data = []
    zone_config = game_config.zone
    for zone in zones:
        zone_pay_records = get_zone_thirty_days_pay_records(zone)
        res = UserZone.call_server_api(zone, 'user_power_rank', {})
        open_days = res[0]
        for country_index, country_data in enumerate(res[1]):
            country_data['zone'] = zone
            country_data['open_days'] = open_days
            country_data['country'] = country_index
            country_data['best_hero_power'] = 0
            country_data['thirty_pay_money'] = 0
            for item in res[4:]:
                if item['country'] != country_index:
                    continue
                if zone_config[zone][8]:
                    old_uid = int(item['uid']) % settings.UIDADD
                    old_zone = int(item['uid']) / settings.UIDADD
                else:
                    old_uid = item['uid']
                    old_zone = zone
                uid_zone = '%s|%s' % (old_uid, old_zone)
                if country_data['best_hero_power'] < item['best_hero_power']:
                    country_data['best_hero_power'] = item['best_hero_power']
                if utils.total_seconds(now - item['t']) < 48 * 3600 and item['rank'] <= 100:
                    country_data['thirty_pay_money'] += zone_pay_records.get(uid_zone, 0)
            data.append(country_data)
    return_res = HttpResponse(json.dumps({'state':'success', 'data': data, 'start_zone': start_zone, 'zone_range': zone_range}))
    return_res['Access-Control-Allow-Origin'] = '*'
    return_res['Access-Control-Allow-Headers'] = 'Content-Type,Content-Length,Accept'
    return return_res

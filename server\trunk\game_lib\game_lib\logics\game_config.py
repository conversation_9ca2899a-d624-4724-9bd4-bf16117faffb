#-*- coding: utf-8 -*-
import cPickle as pickle
from game_lib.models.config import Configs, TestConfigs
from game_lib.models.cache import CacheTable
from game_lib.models.zone import Zones
import time
import datetime
import random
import hashlib
from copy import deepcopy as copy

take_config_type = CacheTable.get('take_config_type', 'setting')
reload_time = time.time()

version = ''
#con = Configs.get('disp')
#if con and con.sub_time:
#    config_value = eval(con.config_value)
#    version = ''.join([str(item) for item in con.sub_time.timetuple()[:6]])
if take_config_type == 'setting':
    ConfigModel = Configs
else:
    ConfigModel = TestConfigs
con = ConfigModel.get('disp')
if con and con.sub_time:
    config_value = eval(con.config_value)


    #version = ''.join([str(item) for item in con.sub_time.timetuple()[:6]])
    version = con.sub_time.strftime('%Y%m%d%H%M%S')
    modify_config_list = config_value.get('modify_config_list', [])

    #last_version = ''.join([str(item) for item in config_value['last_sub_time'].timetuple()[:6]]) if config_value.get('last_sub_time') else None
    last_version = config_value['last_sub_time'].strftime('%Y%m%d%H%M%S') if config_value.get('last_sub_time') else None



#---------------系统配置-------------------------#

server_raise_msg_value = """
{
    '100' : unicode('not login', 'utf-8'),
    '10267' : unicode('暂时无法参与拍卖', 'utf-8'),
}
"""

server_raise_msg_en_value = """
{
    '100' : unicode('not login', 'utf-8'),
    '10267' : unicode('Unable to participate in auction temporarily', 'utf-8'),
}
"""

server_raise_msg_tw_value = """
{
    '100' : unicode('not login', 'utf-8'),
    '10267' : unicode('暫時無法參與拍賣', 'utf-8'),
}
"""

server_raise_msg_kr_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

server_raise_msg_viet_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

server_raise_msg_th_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

server_raise_msg_ja_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

server_raise_msg_ar_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

server_raise_msg_bahasa_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

ope_msg_value = """
{
}
"""

ope_msg_en_value = """
{
}
"""
ope_msg_tw_value = """
{
}
"""

ope_msg_kr_value = """
{
}
"""

ope_msg_viet_value = """
{
}
"""

ope_msg_th_value = """
{
}
"""

ope_msg_ja_value = """
{
}
"""

ope_msg_ar_value = """
{
}
"""

ope_msg_bahasa_value = """
{
}
"""


return_msg_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

return_msg_en_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""
return_msg_tw_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

return_msg_kr_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

return_msg_viet_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

return_msg_th_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

return_msg_ja_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

return_msg_ar_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

return_msg_bahasa_value = """
{
    '100' : unicode('not login', 'utf-8'),
}
"""

zone_value = '''
{
}
'''

zone_pf_value = '''
{
}
'''


system_simple_value = """
{
}
"""

pf_system_simple_value = """
{
}
"""
client_config_value = """
{
}
"""


help_msg_value = """
{
}
"""

disp_value = """
{
    'status': 0,
    'notice': '',
    'wuids': [],
    'color': 'red',
}
"""

pay_config_value = """
{
}
"""

ploy_value = """
{
}
"""
ploy_merge_1_value = """
{
}
"""
ploy_merge_2_value = """
{
}
"""
ploy_merge_3_value = """
{
}
"""
ploy_merge_4_value = """
{
}
"""
ploy_merge_5_value = """
{
}
"""
ploy_merge_6_value = """
{
}
"""
ploy_merge_7_value = """
{
}
"""
ploy_a0_value = """
{
}
"""
ploy_a1_value = """
{
}
"""
ploy_a2_value = """
{
}
"""
ploy_a3_value = """
{
}
"""
ploy_a4_value = """
{
}
"""
ploy_a5_value = """
{
}
"""
ploy_a6_value = """
{
}
"""
festival_value = """
{
}
"""

equip_value = """
{
}
"""

hero_value = """
{
}
"""

star_value = """
{
}
"""

home_value = """
{
}
"""

prop_value = """
{
    "item1022": {
        "name": "代金券",
        "type": 99,
        "rarity": 1,
        "desc": "通用代金券，可用于游戏内充值消费",
        "icon": "item1022",
        "max_num": 999999999,
        "sell_price": 0,
        "can_sell": 0,
        "can_use": 1,
        "can_compose": 0,
        "stackable": 1
    }
}
"""

award_value = """
{
}
"""

army_value = """
{
}
"""

skill_value = """
{
}
"""

world_value = """
{
}
"""

country_value = """
{
}
"""

city_value = """
{
}
"""
city_0_value = """
{
}
"""
city_1_value = """
{
}
"""
city_2_value = """
{
}
"""
city_3_value = """
{
}
"""
city_4_value = """
{
}
"""

inborn_value = """
{
}
"""

fate_value = """
{
}
"""

pub_value = """
{
}
"""

map_value = """
{
}
"""
map_0_value = """
{
}
"""
map_1_value = """
{
}
"""
map_2_value = """
{
}
"""
map_3_value = """
{
}
"""
map_4_value = """
{
}
"""

shop_value = """
{
}
"""


pk_value = """
{
}
"""

pk_robot_value = """
{
}
"""

pve_value = """
{
}
"""

office_value = """
{
}
"""
office37_value = """
{
}
"""

climb_value = """
{
}
"""

climb_0_value = """
{
}
"""

climb_new_value = """
{
}
"""

username_value = """
{
}
"""

fight_value = """
{
}
"""

fight_test_value = """
{
}
"""

shogun_value = """
{
}
"""

pk_yard_value = """
{
}
"""

pk_yard_new_value = """
{
}
"""

title_value = """
{
}
"""
visit_value = """
{
}
"""

city_build_value = """
{
}
"""

task_value = """
{
}
"""

equip_wash_value = """
{
}
"""
credit_value = """
{
}
"""
credit_0_value = """
{
}
"""
effect_value = """
{
}
"""

fight_scene_value = """
{
}
"""

estate_value = """
{
}
"""

science_value = """
{
}
"""

gtask_value = """
{
}
"""

pk_npc_value = """
{
}
"""

attack_city_npc_value = """
{
}
"""

ftask_value = """
{
}
"""

guide_value = """
{
}
"""

guide_0_value = """
{
}
"""

effort_value = """
{
}
"""

npc_info_value = """
{
}
"""

home_effect_value = """
{
}
"""
home_event_value = """
{
}
"""

country_pvp_value = """
{
}
"""

mining_value = """
{
}
"""

waiter_value = """
{
}
"""

country_club_value = """
{
}
"""

merge_zone_value = """
{
}
"""

legend_value = """
{
}
"""

invade_value = """
{
}
"""

country_army_value = """
{
}
"""
bless_hero_value = """
{
}
"""
bless_hero_0_value = """
{
}
"""
bless_hero_npc_value = """
{
}
"""

arena_value = """
{
}
"""

beast_value = """
{
}
"""

new_task_value = """
{
}
"""

legend_awaken_value = """
{
}
"""

honour_value = """
{
}
"""

ctask_value = """
{
}
"""

duplicate_value = """
{
}
"""

hero_skin_value = """
{
}
"""

sp_army_value = """
{
}
"""

duplicate_shop_value = """
{
}
"""

special_value = """
{
}
"""

soul_value = """
{
}
"""

goods_value = """
{
}
"""

tomb_value = """
{
}
"""

color_value = """
{
}
"""

dungeon_date_value = """
{
}
"""

native_value = """
{
}
"""

scheme_value = """
{
}
"""

trade_value = """
{
}
"""

question_value = """
{
}
"""

homeland_value = """
{
}
"""
homeland_build_value = """
{
}
"""
gwent_value = """
{
}
"""
revived_tower_value = """
{
}
"""

player_robot_value = """
{
}
"""

sgame_value = """
{
}
"""

god_value = """
{
}
"""
kunlun_value = """
{
}
"""
city_control_value = """
{
}
"""
puppet_value = """
{
}
"""

story_value = """
{
}
"""

free_value = """
{
}
"""

#######################################################
system_config_name_list = [
        [
            ('server_raise_msg',  u'服务器文字配置', 0),
            ('server_raise_msg_en',  u'服务器文字配置-英文', 0),
            ('server_raise_msg_tw',  u'服务器文字配置-繁体', 0),
            ('server_raise_msg_kr',  u'服务器文字配置-韩文', 0),
            ('server_raise_msg_viet',  u'服务器文字配置-越南', 0),
            ('server_raise_msg_th',  u'服务器文字配置-泰国', 0),
            ('server_raise_msg_ja',  u'服务器文字配置-日文', 0),
            ('server_raise_msg_ar',  u'服务器文字配置-阿拉伯', 0),
            ('server_raise_msg_bahasa',  u'服务器文字配置-印尼', 0),
            ('ope_msg',  u'运营文字配置', 0),
            ('ope_msg_en',  u'运营文字配置-英文', 0),
            ('ope_msg_tw',  u'运营文字配置-繁体', 0),
            ('ope_msg_kr',  u'运营文字配置-韩文', 0),
            ('ope_msg_viet',  u'运营文字配置-越南', 0),
            ('ope_msg_th',  u'运营文字配置-泰国', 0),
            ('ope_msg_ja',  u'运营文字配置-日文', 0),
            ('ope_msg_ar',  u'运营文字配置-阿拉伯', 0),
            ('ope_msg_bahasa',  u'运营文字配置-印尼', 0),
            ('return_msg',  u'文字配置', 0),
            ('return_msg_en',  u'文字配置-英文', 0),
            ('return_msg_tw',  u'文字配置-繁体', 0),
            ('return_msg_kr',  u'文字配置-韩文', 0),
            ('return_msg_viet',  u'文字配置-越南', 0),
            ('return_msg_th',  u'文字配置-泰国', 0),
            ('return_msg_ja',  u'文字配置-日文', 0),
            ('return_msg_ar',  u'文字配置-阿拉伯', 0),
            ('return_msg_bahasa',  u'文字配置-印尼', 0),
            ('ploy',  u'活动配置', 0),
            ('ploy_merge_1',  u'活动配置-合1', 0),
            ('ploy_merge_2',  u'活动配置-合2', 0),
            ('ploy_merge_3',  u'活动配置-合3', 0),
            ('ploy_merge_4',  u'活动配置-合4', 0),
            ('ploy_merge_5',  u'活动配置-合5', 0),
            ('ploy_merge_6',  u'活动配置-合6', 0),
            ('ploy_merge_7',  u'活动配置-合7', 0),
            ('ploy_a0',  u'活动配置-a0', 0),
            ('ploy_a1',  u'活动配置-a1', 0),
            ('ploy_a2',  u'活动配置-a2', 0),
            ('ploy_a3',  u'活动配置-a3', 0),
            ('ploy_a4',  u'活动配置-a4', 0),
            ('ploy_a5',  u'活动配置-a5', 0),
            ('ploy_a6',  u'活动配置-a6', 0),
            ('festival',  u'节日配置', 0),
            ('zone',  u'分区配置', 0),
            ('zone_pf',  u'分区PF控制', 0),
            # ('notice',  u'更新公告', 0),
            ('username', u'玩家名字', 0),

        ],
        [
            ('system_simple',  u'系统杂项', 1),
            ('pf_system_simple',  u'平台杂项', 1),
            ('client_config',  u'前端控制', 1),
            ('disp',  u'运营维护配置', 0),
            ('help_msg',  u'帮助配置', 1),
            ('pk',  u'竞技杂项', 1),
            ('player_robot', u'陪玩配置', 1),
        ],
        [
            ('pay_config',  u'充值', 1),
            ('goods',  u'商品', 1),
            ('waiter',  u'Waiter配置', 1),
            ('color',  u'颜色', 1),
            ('hero',  u'英雄', 1),
            ('inborn',  u'天赋', 1),
            ('fate',  u'宿命', 1),
            ('skill',  u'技能', 1),
            ('special',  u'特性', 1),
            ('equip',  u'宝物', 1),
            ('star',  u'星辰', 1),
            ('fight',  u'战斗配置', 1),
            ('fight_test',  u'战斗测试', 1),
            ('effect',  u'特效', 1),
            ('fight_scene',  u'战斗场景', 1),
            ('home',  u'封地', 1),
            ('home_effect',  u'封地特效', 1),
            ('home_event',  u'封地事件', 1),
        ],
        [
            ('prop',  u'道具', 1),
            ('award',  u'奖池', 1),
            ('army',  u'兵种', 1),
            ('world',  u'世界', 1),
            ('map',  u'地图', 1),
            ('map_0',  u'地图0', 1),
            ('map_1',  u'地图1', 1),
            ('map_2',  u'地图2', 1),
            ('map_3',  u'地图3', 1),
            ('map_4',  u'地图4', 1),
            ('country',  u'国家', 1),
            ('city',  u'城市', 1),
            ('city_0',  u'城市0', 1),
            ('city_1',  u'城市1', 1),
            ('city_2',  u'城市2', 1),
            ('city_3',  u'城市3', 1),
            ('city_4',  u'城市4', 1),
            ('pub',  u'酒馆', 1),
            ('shop',  u'商店', 1),
            ('pve',  u'沙盘演义', 1),
            ('office',  u'爵位', 1),
            ('office37',  u'爵位37', 1),
            ('pk_robot',  u'机器人', 1),
            ('climb',  u'过关斩将', 1),
            ('climb_0',  u'过关斩将0', 1),
            ('climb_new',  u'过关斩将-合服', 1),
            ('pk_yard',  u'比武大会', 1),
            ('pk_yard_new',  u'比武大会New', 1),
            ('title',  u'称号', 1),
            ('shogun',  u'幕府', 1),
            ('visit',  u'拜访', 1),
            ('city_build',  u'城市建筑', 1),
            ('task',  u'任务', 1),
            ('equip_wash',  u'宝物洗炼', 1),
            ('estate',  u'产业', 1),
            ('science',  u'科技', 1),
            ('credit',  u'战功配置', 1),
            ('credit_0',  u'战功配置0', 1),
            ('gtask',  u'政务', 1),
            ('pk_npc',  u'异族入侵', 1),
            ('attack_city_npc',  u'攻城NPC', 1),
            ('ftask',  u'民情', 1),
            ('guide',  u'新手引导', 1),
            ('guide_0',  u'新手引导0', 1),
            ('effort',  u'成就', 1),
            ('npc_info',  u'斥侯情报', 1),
            ('country_pvp',  u'襄阳战', 1),
            ('mining',  u'蓬莱寻宝', 1),
            ('country_club',  u'国家Club', 1),
            ('legend',  u'见证传奇', 1),
        ],
        [
            ('country_army',  u'国家军队', 1),
            ('bless_hero',  u'福将挑战', 1),
            ('bless_hero_0',  u'福将挑战0', 1),
            ('bless_hero_npc',  u'福将挑战NPC', 1),
            ('arena',  u'擂台赛', 1),
            ('beast',  u'八门兽灵', 1),
            ('new_task',  u'朝廷密旨', 1),
            ('legend_awaken',  u'传奇觉醒', 1),
            ('honour',  u'赛季', 1),
            ('ctask',  u'章节任务', 1),
            ('duplicate',  u'跨服战', 1),
            ('hero_skin',  u'英雄皮肤', 1),
            ('sp_army', u'奇士兵种', 1),
            ('duplicate_shop', u'跨服商店', 1),
            ('soul', u'魂玉', 1),
            ('tomb', u'地宫探索', 1),
            ('dungeon_date', u'副本活动', 1),
            ('native', u'特产', 1),
            ('scheme', u'战计', 1),
            ('trade', u'市集', 1),
            ('question', u'题库', 1),
            ('homeland', u'家园', 1),
            ('homeland_build', u'家园建筑', 1),
            ('gwent', u'四维牌', 1),
            ('revived_tower', u'轮回英雄库', 1),
            ('sgame', u'小游戏', 1),
            ('god', u'神灵', 1),
            ('kunlun', u'昆仑活动', 1),
            ('city_control', u'城市接管', 1),
            ('puppet', u'傀儡', 1),
            ('story', u'幻境', 1),
            ('free', u'自由活动', 1),
        ],
    ]



all_config_list = system_config_name_list

## 导入时忽略的配置项
ignore_export_config_list = [
    'zone',
    'zone_pf',
    'notice',
    'pay_config',
    'disp',
    'help_msg',
    'pf_system_simple',
    'ploy',
    'ploy_merge_1',
    'ploy_merge_2',
    'ploy_merge_3',
    'ploy_merge_4',
    'ploy_merge_5',
    'ploy_merge_6',
    'ploy_merge_7',
    'ploy_a0',
    'ploy_a1',
    'ploy_a2',
    'ploy_a3',
    'ploy_a4',
    'ploy_a5',
    'ploy_a6',
    'username',
    # 'festival',
    'duplicate_shop',
    'player_robot'
]

## js推送配置列表
js_push_config_list = [
    'system_simple',
    'hero',
    'equip',
    'star',
    'army',
    'skill',
    'world',
    'inborn',
    'fate',
    'pk_robot',
    'climb',
    'climb_new',
    'fight',
    'title',
    'equip_wash',
    'science',
    'mining',
    'beast',
    'hero_skin',
    'duplicate',
    'sp_army',
    'special',
    'soul',
    'scheme',
    'god',
]

#for client
_all_config_dict = {}
for cc in reduce(lambda x, y: x+y, all_config_list, []):
    _all_config_dict[cc[0]] = cc[1]

## 发送给前端的配置
_client_config_list = ['return_msg', 'ope_msg', 'system_simple', 'pf_system_simple',
                       'client_config', 'pay_config', 'ploy','festival', 'home',
                       'prop', 'pub', 'hero', 'shop', 'army', 'skill', 'equip', 'star', 'pk', 'pve',
                       'zone', 'zone_pf', 'office', 'climb', 'climb_new', 'country', 'country_club',
                       'city', 'office37', 'fight', 'pk_yard', 'pk_yard_new', 'world', 'title', 'shogun', 'visit',
                       'equip_wash', 'estate', 'science', 'effect', 'fight_scene', 'credit', 'fate', 'gtask', 'pk_npc',
                       'city_build', 'attack_city_npc', 'ftask', 'guide', 'task', 'pk_robot', 'effort',
                       'bless_hero', 'bless_hero_npc', 'inborn', 'npc_info', 'country_pvp', 'mining', 'legend',
                       'country_army',
                       'arena', 'beast', 'new_task', 'legend_awaken', 'honour', 'ctask', 'duplicate', 'hero_skin',
                       'sp_army', 'duplicate_shop', 'soul',
                       'tomb', 'goods', 'color', 'dungeon_date', 'map', 'home_effect', 'native', 'scheme',
                       'trade', 'special', 'homeland', 'homeland_build',
                       'gwent', 'revived_tower', 'sgame', 'god', 'kunlun',
                       'city_control', 'home_event', 'puppet', 'story', 'free']

## 资源加载阶段需要发给前端的配置，不在此列表中的配置在登录阶段发送给前端
before_client_config_list = ['return_msg', 'ope_msg', 'system_simple', 'pf_system_simple','client_config','zone', 'zone_pf', 'color']

## 不发送前端，但是需要发布的额外配置
_publish_config_extra = ['server_raise_msg', 'server_raise_msg_en', 'server_raise_msg_tw', 'server_raise_msg_kr',
                         'server_raise_msg_viet', 'server_raise_msg_th', 'server_raise_msg_ja', 'server_raise_msg_ar',
                         'server_raise_msg_bahasa', 'question', 'return_msg_en', 'return_msg_tw', 'return_msg_kr',
                         'return_msg_viet', 'help_msg',
                         'return_msg_th', 'return_msg_ja', 'return_msg_ar', 'ope_msg_en', 'ope_msg_tw', 'ope_msg_kr',
                         'ope_msg_viet', 'ope_msg_th', 'ope_msg_ja',
                         'ope_msg_ar', 'award', 'ploy_merge_1', 'ploy_merge_2',
                         'ploy_merge_3', 'ploy_merge_4', 'ploy_merge_5',
                         'ploy_merge_6', 'ploy_merge_7',
                         'city_0', 'city_1', 'city_2', 'city_3', 'city_4', 'map_0', 'map_1',
                         'map_2', 'map_3', 'map_4', 'ploy_a0',
                         'ploy_a1', 'ploy_a2', 'ploy_a3', 'ploy_a4', 'ploy_a5',
                         'ploy_a6', 'guide_0',
                         'credit_0', 'bless_hero_0', 'climb_0', 'player_robot',
                         'fight_test']


_client_config_map = {}
client_config_dict = {}
publish_config_extra = {}
for key in _client_config_list:
    client_config_dict[key] = (_all_config_dict[_client_config_map.get(key, key)], _client_config_map.get(key, key))
    # if key == 'festival':
    #     with open(log_file, "a") as f:
    #         f.write("key:==="+str(key) + "\n"+"client_config_dict[key]:==="+str(client_config_dict[key]) + "\n")
for key in _publish_config_extra:
    publish_config_extra[key] = (_all_config_dict[_client_config_map.get(key, key)], _client_config_map.get(key, key))
#end

config_version_dict = {}

for item in all_config_list:
    for _item in item:
        config_name = _item[0]
        config = ConfigModel.get(config_name)
        if not config:
            config = ConfigModel(config_name)
            config.config_value = eval(config_name + '_value')
            config.sub_time = datetime.datetime.now()
            config.save()

        if config_name in ['pf_system_simple', 'client_config', 'system_simple']:
            _config_version = config_version_dict.get('system_simple')
            if not _config_version:
                _config_version = config.sub_time.strftime('%Y%m%d%H%M%S') if config.sub_time else ''
            else:
                if datetime.datetime.strptime(_config_version, '%Y%m%d%H%M%S') < config.sub_time:
                    _config_version = config.sub_time.strftime('%Y%m%d%H%M%S') if config.sub_time else ''
            config_version_dict['system_simple'] = _config_version
        else:
            _config_version = config.sub_time.strftime('%Y%m%d%H%M%S') if config.sub_time else ''
            config_version_dict[config_name] = _config_version

        if config_name in ['pf_system_simple', 'client_config']:
            continue
        if config_name == 'zone':
            zone = Zones.get_zone_config()
        else:
                        # 调试输出festival配置加载
            # if config_name == 'festival':
            #     with open(log_file, "a") as f:
            #         f.write("=== DEBUG: Loading festival config ===\n")
            #         f.write("config.config_value: " + str(config.config_value) + "\n")
            #         # f.write("festival variable value: " + str(eval(config_name)) + "\n")
            #         # f.write("festival type: " + str(type(eval(config_name))) + "\n")
            #         f.write("=====================================\n")
            exec(config_name + '=eval(str(config.config_value))')
            
#系统杂项，平台系统杂项，前端控制合在一起
pf_system_simple_config_value = eval(ConfigModel.get('pf_system_simple').config_value)
client_config_value = eval(ConfigModel.get('client_config').config_value)
system_simple.update(pf_system_simple_config_value)
system_simple.update(client_config_value)
science_type = {}
for k,v in science.items():
    stype = v['type']
    if not science_type.has_key(stype):
        science_type[stype] = {}
    if stype == 'day_get':
        sstype = 'day_get'
        ssvalue = v['day_get']
    elif stype == 'interior':
        sstype,ssvalue = v[stype]
    else:
        continue
    if not science_type[stype].has_key(sstype):
        science_type[stype][sstype] = {}
    science_type[stype][sstype][k] = ssvalue

science['science_type'] = science_type


## 此段处理迁移至长链server中处理
#country_task_ids = []
#task_type_dict = {}
#for item in ['daily', 'main', 'country','common']:
#    if item not in task:
#        continue
#    for k,v in task[item].items():
#        if task_type_dict.has_key(v['type']):
#            task_type_dict[v['type']].append([k,item])
#        else:
#            task_type_dict[v['type']] = [[k,item]]
#        if item == 'country':
#            country_task_ids.append(k)
#    if item == 'country':
#        for c in task.get('country_maps', []):
#            for k,v in c.items():
#                if k in country_task_ids:
#                    continue
#                if task_type_dict.has_key(v['type']):
#                    task_type_dict[v['type']].append([k, item])
#                else:
#                    task_type_dict[v['type']] = [[k, item]]
#                country_task_ids.append(k)
#for k,v in ctask.get('task', {}).items():
#    if task_type_dict.has_key(v['type']):
#        task_type_dict[v['type']].append([k,'chapter'])
#    else:
#        task_type_dict[v['type']]=[[k,'chapter']]
#task['task_type_dict'] = task_type_dict

user_effort_type_dict = {}
for k,v in effort.items():
    if user_effort_type_dict.has_key(v['type']):
        user_effort_type_dict[v['type']].append(k)
    else:
        user_effort_type_dict[v['type']] = [k]
effort['effort_type_dict'] = user_effort_type_dict

# new_config_md5_version = {}
# for k in _all_config_dict:
#     if k in ['pf_system_simple', 'client_config']:
#         continue
#     if k == 'zone':
#         _md5_str = Zones.get_current_md5()
#     else:
#         _md5_str = hashlib.md5(str(eval(k))).hexdigest()
#     new_config_md5_version[k] = _md5_str

#guild_effort_type_dict = {}
#for k,v in guild['achievement'].items():
#    if v['type']:
#        if guild_effort_type_dict.has_key(v['type']):
#            guild_effort_type_dict[v['type']].append(k)
#        else:
#            guild_effort_type_dict[v['type']] = [k]
#guild['effort_type_dict'] = guild_effort_type_dict


__all__ = all_config_list

package sg.model {

    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;

    public class ModelMB extends ModelBase {
        public function isCanMBPay(pids:String):Boolean {
            var cfg:Object = ConfigServer.pay_config[pids];
            // 改为使用代金券检查
            if (ModelManager.instance.modelUser.getVoucherAmount() >= cfg[0]) {
                return true;
            }
            return false;
        }

        public function isCanLCoinPay(pids:String):Boolean {
            var cfg:Object = ConfigServer.pay_config[pids];
            if (ModelManager.instance.modelUser.lcoin >= cfg[0]) {
                return true;
            }
            return false;
        }
    }
}

package sg.net
{
    import laya.events.EventDispatcher;
    import laya.net.Socket;
    import laya.events.Event;
    import laya.utils.Byte;
    import laya.utils.Handler;
	import sg.crossService.CrossServiceFacade;
	import sg.crossService.model.CrossServiceUserModel;
    import sg.manager.ModelManager;
    import sg.cfg.ConfigServer;
	import sg.map.utils.ArrayUtils;
	import sg.scene.view.TestButton;
	import sg.test.testpakcage.TestPackage;
    import sg.utils.Tools;
    import sg.guide.model.ModelGuide;
    import sg.manager.ViewManager;
    import sg.model.ModelGame;
    import laya.utils.Browser;
    import sg.utils.ZlibInflate;
    import sg.cfg.ConfigApp;

    public class NetSocket extends EventDispatcher{
        public static const EVENT_SOCKET_CLOSE:String = "event_socket_close";
        public static const EVENT_SOCKET_ERROR:String = "event_socket_error";
        public static const EVENT_SOCKET_OPENED:String = "event_socket_opened";
		public static const EVENT_SOCKET_RELOAD:String = "event_socket_reload";
        public static const EVENT_SOCKET_RE_CODE_ERROR:String = "event_socket_re_code_error";
        public static const EVENT_SOCKET_SEND_TO:String = "event_socket_send_to";
        public static const EVENT_SOCKET_RECEIVE_FROM:String = "event_socket_receive_from";
        public static const EVENT_SOCKET_RECEIVE_TO:String = "event_socket_receive_to";
        //
        public static var timeOutTimer:Number = 15000;//超时时间ms
        public static var pingTimer:Number = 10000;//ping时间ms
        public var timeoutPkg:Object = {};//超时包记录
		public var groups:Object = {};//侦听分组 zhuda添加
        public var service_force_close:Boolean = false;//服务器强制关闭
        //
        private static var sNetSocket:NetSocket = null;
        private static var isSpecailClose:Boolean = false;

        public static var sMethodName:String="";
        public static var sSendData:Object;
        public static var sRetrunMsg:String;
        public static var clssName_1:String = "本服"
        public static var clssName_2:String = "跨服战"
		public var className:String = clssName_1;
		public var sleepTime:Array = [0, 0, 0];
        public var connectNum:Number = 0;
        public var login_succ_num:Number = 0;
		// public var timeoutTempArr:Array = [];
        public var reConnetIng:Boolean = false;
		public  static function get instance():NetSocket{
			return sNetSocket ||= new NetSocket();
		}
		
		public function get socket():Socket {
			return mSocket;
		}
		public function NetSocket(){
			
		}
        public function setSocketName(name:String):void{
            className = name;
        }
        public  var HTTP_URL:String = "";
        public function setURLtoConnect(ip:String,c:String,isSSL:Boolean):void{
            var protocol:String = isSSL?"wss:":"ws:";//协议
            HTTP_URL = protocol+"//"+ip+":"+c+"/gateway/";
            // HTTP_URL = protocol+"//************/";
            // Trace.log("=====>>socket net=====>>",HTTP_URL);
            clear();
            init();
        }
        private var mSocket:Socket;
        private var closeTimes:int = 0;
        //
        public function init():void{
            this.mSocket = new Socket();
            this.service_force_close = false;
            this.closeTimes = 0;
            // this.mSocket.endian = Byte.LITTLE_ENDIAN;//这里我们采用小端；
            this.requestPkg = {};
            // this.mSocket.connect("*************",8000);
            this.mSocket.on(Event.OPEN,this,openHandler);
            this.mSocket.on(Event.MESSAGE,this,receiveHandler);
            this.mSocket.on(Event.CLOSE,this,closeHandler);
            this.mSocket.on(Event.ERROR,this,errorHandler);
            //
            if(!isSpecailClose){
                this.mSocket.connectByUrl(HTTP_URL);
            }
			
			Laya.stage.on(Event.VISIBILITY_CHANGE,this,this.visibility_change);
            //
            // Trace.log("====>>>> "+className+"socket 初始化 init 第一次连接",HTTP_URL);
        }
		
		private function visibility_change(e:Event):void {
			TestButton.log("隐藏与显示" + Laya.stage.isVisibility);
			this.sleepTime[Laya.stage.isVisibility ? 0 : 1] = Browser.now();
		}
        public function clear():void{
            this.service_force_close = true;
            if(this.mSocket){
                this.mSocket.offAll(Event.OPEN);
                this.mSocket.offAll(Event.MESSAGE);
                this.mSocket.offAll(Event.CLOSE);
                this.mSocket.offAll(Event.ERROR);
                this.mSocket.close();
                // this.mSocket.cleanSocket();
            }
            this.mSocket = null;
        }
        public function testClose():void{
            this.mSocket.close();
        }
        public function reConnet():void{
            this.closeTimes ++;
            this.service_force_close = false;
            
            if(this.closeTimes>9){
                Trace.log("====>>>> "+className+"N次重连失败的处理==", HTTP_URL, this.closeTimes);
                //Trace.msg("[重连失败的处理 "+this.closeTimes);                
                this.closeTimes = 0;
				TestButton.log("reConnet1");
                //
                this.closeSocket({msg:Tools.getMsgById("_lht57")});
                return;
            }
            // this.mSocket.cleanSocket();
            if(!isSpecailClose){
                Trace.log("====>>>> "+className+"开始重新连接 == ", HTTP_URL, this.closeTimes);
                //Trace.msg("[开始重新连接 "+this.closeTimes);
                TestButton.log("reConnet2");
                this.mSocket.connectByUrl(HTTP_URL);
            }
			
        }
        public function openHandler(event:Object = null):void
        {
			TestButton.log("openHandler0");
			//关闭太久之后。 再重新打开。 很多包都会接收不到。这里我直接就给他退到主界面了。防止内容错误			
			var now:Number = Browser.now();
			// if (Laya.stage.isVisibility && now - this.sleepTime[0] < 1000 * 30) {//舞台打开，并且允许有30秒卡主连接时间
			// 	var sleepTime:Number = (this.sleepTime[0] - this.sleepTime[1]);
			// 	var receiveTime:Number = now - this.sleepTime[2];
			// 	if (sleepTime > 90 * 1000 && receiveTime > 60 * 1000) {// 关闭一分半钟以上。 并且有一分钟没接收到信息。网络重新打开的话。就认为是这一分钟没有收到包了。就可以踢了！
			// 		this.event(NetSocket.EVENT_SOCKET_RELOAD, sleepTime);					
			// 		TestButton.log("openHandler1");
			// 		return
			// 	}
			// }
			TestButton.log("openHandler2");
            // timeoutTempArr = [];
            reConnetIng = false;
            if(this.closeTimes>0){
                Trace.log("<<<<==== "+className+"N次重新连接 成功打开 <<<<====", this.closeTimes, HTTP_URL);
                //Trace.msg("[重新连接成功 "+this.closeTimes);
            }
            else{
                Trace.log("<<<<==== "+className+"首次连接 成功打开 <<<<====", event, HTTP_URL);
            }
            login_succ_num = 0;
            this.event(EVENT_SOCKET_OPENED);
            this.closeTimes = 0;
            this.checkConnected();
        }
        public function checkConnected():void
        {
            if(ConfigApp.isTest){
                return;
            }
            if(className == clssName_2){
                return;    
            }
            if(login_succ_num<=0){
                connectNum = 2;
            }
            else{
                connectNum = 2;
                // var url:String = ConfigApp.get_ping_url()+"?v="+new Date().getTime();
                // NetHttp.instance.getRequest(url,Handler.create(null, function(np:Object):void {
                //     // Trace.log("== ping trace ==",url,np);
                //     if(np && connectNum==1){
                //         connectNum = 2;
                //     }
                // }));
            }
			TestButton.log("checkConnecte1");
            Laya.timer.clear(this,this.checkConnectedResult);
            Laya.timer.once(pingTimer,this,this.checkConnectedResult);
        }
        private function checkConnectedResult():void{
            if(connectNum==2){
                checkConnected();
            }
            else{
                // this.closeSocketTimeOut({msg:Tools.getMsgById("msg_NetSocket_0")});
            }
        }
        public function receiveHandler(msg:Object = null):void
        {
			if (TestButton.test) {
				// Trace.log("略过" + '【' + (reData?reData.method:'xxx') + '】' + reData);
				return;
			}
            var arrayBuffer:* = msg;
            if(msg is ArrayBuffer){
                //特殊类型需要解压
                arrayBuffer = ZlibInflate.unzip(msg);
            }
            var reData:Object = JSON.parse(arrayBuffer + "");
			this.sleepTime[2] = Browser.now();

			Trace.log("<<<<==== "+className+"socket 接收数据 receive <<<<====", '【' + (reData?reData.method:'xxx') + '】', reData);
            // if(reData && reData.method === "chat"){
            //     ViewManager.instance.showTipsTxt(className+"--"+((reData.data is Array)?reData.data[3][0]:reData.data.chat[3][0]));
            // }
			TestButton.log("<----" + (reData?reData.method:"xxx"));
            
            if(this.isSpecail(reData)){
                this.closeSocket(reData);
                return;
            }
            this.recevie(reData);
        }
        public function closeSocket(data:Object):void
        {
            this.service_force_close = true;
            this.event(EVENT_SOCKET_CLOSE,[this.service_force_close,data]);     
            if(this.mSocket){
                this.mSocket.close();
                // this.mSocket.cleanSocket();
                // this.mSocket = null;
            }           
        }
        private function closeSocketTimeOut(data:Object):void
        {
            if(reConnetIng){
                checkConnected();
                return;
            }
            if(this.mSocket){
                if(data){
                    Trace.log("<<<<==== "+className+"网络ping功能出现超时 <<<<====");
                    //Trace.msg("<请检查网络连接>");
                }
                this.mSocket.close();
                // this.mSocket.cleanSocket();
                // this.mSocket = null;
            }
        }
        public function closeHandler(e:Object= null):void
        {
            Trace.log("<<<<==== "+className+"socket 关闭 closeHandler <<<<====");
            if (this.service_force_close){
				TestButton.log("closeHandler0");
                return;
            }
            reConnetIng = true;
            Laya.timer.clear(this,this.reConnet);
            Laya.timer.once(1000, this, this.reConnet);
			TestButton.log("closeHandler1");
            //
            this.event(EVENT_SOCKET_CLOSE,[false,null]);
        }
        private function errorHandler(e:Object = null):void
        {
            Trace.log("<<<<==== "+className+"socket 错误 errorHandler <<<<====");
			TestButton.log("errorHandler0");
            var restar:Boolean = false;
            if(Browser.window){
                restar = true;
            }
            this.event(EVENT_SOCKET_ERROR,[true,{msg:Tools.getMsgById("_lht56")},restar]);
        }
        //
        /**
		 * 等待接收服务器的包的字典。 下标是pid 不停的向上增长。接收方法名pid键值，内容是数组。数组每一项是pkg
		 */
		public var requestPkg : Object = {};
		/**
		 * 通过存储class的键值来找寻对应执行者的实现Inotify的接口 接收方法名string的键值，内容是数组。数组每一项是pkg
		 */
		public var notificationMap : Object = {};
		public var isCrossServiced:Boolean = false;
 //————————————————————————————————————————————————以下是方法————————————————————————————————————————
		
		public function isSpecail(re:Object):Boolean
        {
            if(re["method"]== "close"){
                isSpecailClose = true;
                return true;
            }        
            return false;    
        }
		public function send(method:String, sendData:Object, handler:Handler = null, otherData:* = null):NetPackage {
			/**************************记录当前需要返回的包******************************/
            //
            sMethodName=method;
            sSendData=sendData;
            TestPackage.addPackage(method, sendData, true, this);
            //
			var pkg:NetPackage = NetPackage.createPkg(0);
			pkg.sendMethod = method;
			pkg.sendData = sendData;
			pkg.otherData = otherData;
            pkg.sendTime = new Date().getTime();
            pkg.receiveHandler = handler;
            if (ModelGuide.forceGuide()) {
                pkg.guideData = ModelGuide.getGuideData(method);
            }
			return sendPkg(pkg, handler);
		}
		
		
		/**
		 * 
		 * @param	pkg
		 * @param	handler
		 * @return
		 */
		public function sendPkg(pkg : NetPackage, handler:Handler = null):NetPackage {
			/**************************记录当前需要返回的包******************************/
			//放到requestPkg下面 进行存储 包括超过时长 也要检测。
            
            this.requestPkg[pkg.pid]=pkg;
            // Trace.log("-----11111-----",pkg.pid,pkg.sendMethod);
            //
            if(this.mSocket && this.mSocket.connected){
                this.event(EVENT_SOCKET_SEND_TO,[pkg.sendMethod,true]);
                pkg.setTimeOut(NetSocket.timeOutTimer);
                this.mSocket.send(pkg.getSendDataJson());
            }
			return pkg;
		}
        
		public function selfSendPackageIsOver(pkg:NetPackage,isTimeout:Boolean):void{
            var method:String = "";
            if(pkg){
                var pid:* = pkg.pid;
                method = pkg.sendMethod;
                if(isTimeout){
                    // this.removeRequest(pid,method);
                    this.setTimeoutPkgRequest(pid,method);
                    // timeoutTempArr.push(method);
                    // if(timeoutTempArr.length<2){
                        Trace.log("<<<<==== "+className + " : "+ pid+" : "+method+"出现超时 <<<<====");
                        //Trace.msg("[请检查网络连接]"); 
                        this.closeSocketTimeOut(null);
                    // }
                }
                else{
                    pkg.clearTimeOut();
                    //
                    if(this.requestPkg.hasOwnProperty(pid)){
                        delete this.requestPkg[pid];
                    }
                }
            } 
            this.event(EVENT_SOCKET_RECEIVE_FROM,[method,false, isTimeout]);       
        }
		public function recevie(obj:Object):void {
            if(obj == null){
                return;
            }
			var pkg:NetPackage = null;
            if (obj.hasOwnProperty("time")){
				if (this.isCrossServiced) {
					CrossServiceFacade.checkServerTime(Tools.getTimeStamp(obj["time"]), true);					
				}else {
					ConfigServer.checkServerTime(Tools.getTimeStamp(obj["time"]), true);
				}
                
            }
			if (this.isCrossServiced && obj.hasOwnProperty("data") && obj.data.hasOwnProperty("user")) {
				CrossServiceUserModel.instance.updateUserData(obj.data);
			}
			
            var code:Number = -1;
            if(obj.hasOwnProperty("code")){
                code = Number(obj.code);
            }
			
			TestPackage.addPackage(obj.method, obj.data, false, this);
			
            var isMe:Boolean = false;
			if(obj["pid"]){
                var pid:Number = obj.pid;
                if(pid > 0) {
                    //从request里取。
                    pkg = this.requestPkg[pid];
                    if (pkg){
                        //
                        this.checkTimeoutPkg(pid,obj.method);
                        // 
                        this.selfSendPackageIsOver(pkg,false);
                        //
                        if(pkg.sendMethod == obj.method){
                            isMe = true;
                            pkg.receiveMethod = obj.method;
                            pkg.receiveData = obj.data;
                            pkg.reTime = new Date().getTime();
                            if(pkg.isTimeout(NetSocket.timeOutTimer)){
                                Trace.log("==>>超时包==>>被执行了==>>"+className+"==>>",obj);
                            }
                            if(code == 0){//正常返回
                                pkg.notify();
                                sRetrunMsg = "";
                            }
                            else{
                                this.checkErrorCode(code,obj);
                            }
                        }
                        pkg.clear();
                    }
                }
            }
            if ((code == -1 || code == 0)){// && !isMe) {
				this.notifyOnlyListeners(obj);
			}
            //
            this.event(EVENT_SOCKET_RECEIVE_TO,[obj,isMe]);
            //这里要再次检查 requestPkg 里面有 死包，用发送时间和检查时间做 超时
		}
        public function checkErrorCode(code:Number,obj:Object):void
        {
            if(code == 1000){//数据配置有更新
                this.closeSocket({msg:Tools.getMsgById("msg_NetSocket_1")});
            }else if(code == 500){
                var hintMsg:String = Tools.getMsgById("msg_NetSocket_2") + code;
                if (obj.data && obj.data.msg) {
                    obj.data.msg = hintMsg + ' ' + obj.data.msg;
                } else {
                    ViewManager.instance.showTipsTxt(hintMsg);
                }
            }else if(code >= 2000 && code <= 3000){//特殊错误
                if(obj.data){
                    obj.data['code'] = code;
                }
                this.closeSocket(obj.data);
            }
            
            if(code){
                this.event(EVENT_SOCKET_RE_CODE_ERROR, obj);
            }
            sRetrunMsg = code + "   " + (obj.data && obj.data.msg ? obj.data.msg : "");
        }
        public function notifyOnlyListeners(obj:Object):void{
            var observers : Array = notificationMap[obj.method];
            var pkg:NetPackage = null;
            if (observers) {
                Trace.log("**** 服务器主动通知过来的消息 ***",obj.method,obj);
                sRetrunMsg = "";
                if(obj.hasOwnProperty("code")){              
                    this.checkErrorCode(Number(obj.code),obj);
                }
                if (obj.receipt) {
                    send('receive_receipt', { push_id: obj.push_id });
                }
                for (var i:int = 0, len:int = observers.length; i < len; i++) {
                    pkg = (observers[i] as NetPackage);
                    pkg.receiveData = obj.data;
                    pkg.notify();
                    // pkg.clear();
                }
            }
        }
// ————————————————————————————————————————————————————接收 注册包————————————————————————————————————————————————————————————————

		
		
		/**
		 * 注册观察者， 这个一般用于长连接的时候， 后台主动发起的！ 如果是那种一来一回的话 就直接通过sendRequest发起即可。
		 * @param	type
		 * @param	handler
		 * @param	group   分组，指定后可以批量移除，但所有Handler为once的指定分组无效（防止对象池引用错误）
		 * @return
		 */
		public function registerHandler(type : String, handler : Handler, group:String = null) : Boolean {	
            if(hasRegisterHandler(type,handler)){return false}
            notificationMap[type] ||= [];
            var observers : Array = notificationMap[type];
            var pkg:NetPackage = NetPackage.createPkg();
            pkg.sendMethod = type;
            pkg.receiveMethod = type;
            pkg.receiveHandler = handler;
            observers.push(pkg);
			
			if (group && !handler.once){
				if(!this.groups[group])
					this.groups[group] = [];
				(this.groups[group] as Array).push([type, handler]);
			}
			return true;
		}
		
		/**
		 * 移除所有观察者， 若指定type，仅删除此类
		 * @param	type
		 * @return
		 */
		public function removeAllHandler(type:String = null) : void {
			if (type == null) {
				notificationMap = {};
			} else {
				delete notificationMap[type];
			}
		}
		/**
		 * 移除特定分组的所有观察者
		 * @param	group
		 * @return
		 */
		public function removeGroupHandler(group:String) : void {
			var arr:Array = this.groups[group];
			if (arr){
				var i:int;
				var arrOne:Array;
				var type:String;
				var handler: Handler;
				for (i = arr.length - 1; i >= 0; i-- ){
					arrOne = arr[i];
					type = arrOne[0];
					handler = arrOne[1];
					this.removeHandler(type, handler);
				}
				delete this.groups[group];
			}
		}
		
		public function hasRegisterHandler(type:String, handler : Handler,del:Boolean = false):Boolean {
			if (!notificationMap[type]) return false;
			// return notificationMap[cls].some(function(p:NetPackage, i:int, arr:Array):Boolean{return p.receiveHandler == handler});
            var arr:Array = notificationMap[type];
            var len:int = arr.length;
            var b:Boolean = false;
            var delIndex:int = -1;
            for(var index:int = 0; index < len; index++)
            {
				var np:NetPackage = arr[index];
				if(np.receiveHandler == handler)
				{
                   b = true;
                   delIndex = index;
                   break;
				}  
            }
            if(del && delIndex>=0){
                arr.splice(delIndex,1);
            }
            return b;
		}

		/**
		 * 移出对应 registerHandler注册的观察者。
		 * @param	type
		 * @param	handler
		 * @return
		 */
		public function removeHandler(type : String, handler : Handler) : Boolean {
			//找到对应的pkg 移除掉。

			return hasRegisterHandler(type,handler,true);
		}
		
		/**
		 * 如果中途发送 想要取消的话 那么就调用此方法。 就可以移出了！
		 * @param	id
		 */
		public function setTimeoutPkgRequest(id : *,method:String) : void {
            if(this.requestPkg.hasOwnProperty(id)){
                if(!this.timeoutPkg.hasOwnProperty(id)){
                    this.timeoutPkg[id] = [method];
                    Trace.log("==>>超时包==>>有新的包==>>",id,method,this.timeoutPkg);
                }
            }
		}  
        public function checkTimeoutPkg(id : *,method:String):void{
            if(this.timeoutPkg.hasOwnProperty(id)){
                this.timeoutPkg[id].push(method);
                Trace.log("==>>超时包==>>已有超时包==>>",id,method,this.timeoutPkg,"==>>再次返回");
            }
        }     
    }
}
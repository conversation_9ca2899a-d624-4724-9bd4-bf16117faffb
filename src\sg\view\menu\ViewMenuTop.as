package sg.view.menu
{
	import laya.ui.Box;
	import ui.menu.topUI;
	import sg.view.com.ItemBase;
	import sg.manager.ModelManager;
	import sg.model.ModelUser;
	import laya.ui.Label;
	import laya.utils.Tween;
	import laya.utils.Handler;
	import sg.utils.Tools;
	import laya.events.Event;
	import sg.manager.ViewManager;
	import sg.cfg.ConfigClass;
	import sg.boundFor.GotoManager;
	import sg.model.ModelGame;
	import sg.cfg.ConfigServer;
	import sg.model.ModelItem;
	import laya.ui.Image;
	import sg.model.ModelBuiding;

	/**
	 * ...
	 * <AUTHOR>
	public class ViewMenuTop extends Box {
		protected var _ui:ItemBase;
		public var coin_red:Image;
		public var coin_add:Image;
		private var userModel:ModelUser = ModelManager.instance.modelUser;
		private var str_arr:Array = ModelBuiding.material_type;
		private var tweenCount:Object = {};
		private var voucherBtn:Image;
		private var voucherVar:*;
		public function ViewMenuTop(ui:ItemBase= null) {
			ui ||= new topUI();
			addChild(_ui = ui);
			coin_red = _ui['coin_red'];
			coin_add = _ui['coin_add'];
      centerX = 0;
			str_arr.forEach(_initResourse, this);
			_initVoucher(); // 初始化代金券显示
			userModel.on(ModelUser.EVENT_TOP_UPDATE,this,listenCallBack);
			ViewManager.instance.on(ViewManager.CHANGE_MENU_TOP_VISIBLE, this, this.setVisible);
			coin_red.visible = false;
			coin_add.visible = !ModelGame.unlock(null,"pay").stop && userModel.canPay;
			checkCoupon();
			this.mouseThrough = true;
		}

		private function _initResourse(id:String):void {
			var btn:Image = _ui[id];
			btn.on(Event.CLICK, this, click, [id]);
			_ui[id + '_var'].setData(ModelItem.getItemIconAssetUI(id, false),Tools.textSytle(userModel[id]));
		}

		/**
		 * 初始化代金券显示
		 */
		private function _initVoucher():void {
			// 如果UI中有代金券相关组件，则初始化
			if (_ui["voucher"]) {
				voucherBtn = _ui["voucher"];
				voucherVar = _ui["voucher_var"];
				voucherBtn.on(Event.CLICK, this, click, ["voucher"]);
				voucherVar.setData(ModelItem.getItemIconAssetUI("voucher001", false), Tools.textSytle(userModel.getVoucherAmount()));
			}
		}

		public function setItemPosDict():void {
			str_arr.forEach(function(id:String):void {
				var btn:Image = _ui[id];
				ViewManager.instance.setItemPosDict(id, btn);
			}, this);
		}

		private function setVisible(value:Boolean):void {
			this.visible = value;
		}

		/**
		 * 检查有没有快过期的抵扣券
		 */
		private function checkCoupon():void{
			var delay:Number = ConfigServer.system_simple.npc_info_time || 15;
			this.timer.clear(this,this.checkCoupon);
			this.timer.once(delay * Tools.oneSecondMilli,this,this.checkCoupon);
			if(!userModel.sale_pay || !userModel.canPay) return;

			var arr:Array = userModel.sale_pay;
			var now:Number = ConfigServer.getServerTimer();
			var couponExpiringSoon:Boolean = arr.some(function(arr2:Array):Boolean {
				var time:Number =Tools.getTimeStamp(arr2[2]);
				return now < time && now + 24 * Tools.oneHourMilli > time; // 一天后过期
			});
			if (couponExpiringSoon) {
				coin_red.visible = true;
				redTween();
			} else {
				coin_red.visible = false;
				Tween.clearAll(coin_red);
			}
		}

		private function redTween():void {
			var duration:int = 1000;
			Tween.to(coin_red, {alpha: 0.7}, duration);
			Tween.to(coin_red, {alpha: 1}, duration, null, Handler.create(this, redTween), duration);
		}

		protected function click(id:String):void{
			if (id === 'coin') {
				GotoManager.boundForPanel(GotoManager.VIEW_PAY_TEST);
			} else if (id === 'voucher') {
				// 点击代金券，显示代金券充值界面
				GotoManager.boundForPanel(GotoManager.VIEW_PAY_TEST);
			} else {
				ViewManager.instance.showView(ConfigClass.VIEW_BAG_SOURSE, id);
			}
		}

		public function listenCallBack(obj:Object):void {
			obj && str_arr.forEach(function(id:String):void {
				obj[id] && labelTween(id, obj[id]);
			});
			// 更新代金券显示
			if (voucherVar) {
				voucherVar.setNum(Tools.textSytle(userModel.getVoucherAmount()));
			}
		}

		public function labelTween(id:String, num:Number):void{
			var l:Label = new Label();
			var btn:Image = _ui[id];
			l.pos(btn.x + btn.width * 0.3, btn.height * 0.5);
			l.fontSize = 18;
			if(num < 0) {
				l.text = num + "";
				l.color = "#ff1e00";
			} else if (num > 0) {
				l.text = "+" + num;
				l.color = "#3dff00";
			}
			tweenCount[id] = (tweenCount[id] || 0) + 1;
			var delayNum:Number = num > 0 ? 1000 + tweenCount[id] * 200 : 0;
			if(delayNum !== 0) {
				Laya.timer.once(delayNum, this, tweenFunc, [l, id], false);
			} else {
				this.tweenFunc(l, id);
			}
		}

		private function tweenFunc(l:Label, id:String):void {
			this.addChild(l);
			Tween.to(l, {y: l.y - 20}, 1200, null, Handler.create(this,tweenCallBack,[l, id]));
		}

		private function tweenCallBack(ll:Label, id:String):void {
			tweenCount[id] && tweenCount[id]--;
			ll.destroy();
			_ui[id + '_var'].setNum(Tools.textSytle(userModel[id]));
		}

		override public function destroy(destroyChild:Boolean = true):void {
			userModel.off(ModelUser.EVENT_TOP_UPDATE, this, listenCallBack);
			super.destroy(destroyChild);
		}
	}
}